/**
 * ConfigurablePage 组件常量定义
 */

// 组件类型枚举
export const COMPONENT_TYPES = {
  // 表单组件类型
  INPUT: 'input',
  INPUT_NUMBER: 'input-number',
  SELECT: 'select',
  RADIO: 'radio',
  CASCADER: 'cascader',
  SWITCH: 'switch',
  TIME_PICKER: 'time-picker',
  DATE_PICKER: 'date-picker',
  DATE_RANGE: 'date-range',
  DATETIME_PICKER: 'datetime-picker',
  RATE: 'rate',
  TEXTAREA: 'textarea',

  // 表格列类型
  TAG: 'tag',
  ACTIONS: 'actions',

  // 自定义渲染
  RENDER: 'render'
}

// 表单组件类型验证数组
export const FORM_COMPONENT_TYPES = [
  COMPONENT_TYPES.INPUT,
  COMPONENT_TYPES.SELECT,
  COMPONENT_TYPES.DATE,
  COMPONENT_TYPES.TEXTAREA,
  COMPONENT_TYPES.SWITCH
]

// 表格列类型验证数组
export const TABLE_COLUMN_TYPES = [
  COMPONENT_TYPES.TAG,
  COMPONENT_TYPES.ACTIONS
]

// 所有支持的组件类型
export const ALL_COMPONENT_TYPES = [
  ...FORM_COMPONENT_TYPES,
  ...TABLE_COLUMN_TYPES,
  COMPONENT_TYPES.RENDER
]

// 按钮类型枚举
export const BUTTON_TYPES = {
  PRIMARY: 'primary',
  SUCCESS: 'success',
  WARNING: 'warning',
  DANGER: 'danger',
  INFO: 'info',
  TEXT: 'text'
}

// 标签类型枚举
export const TAG_TYPES = {
  PRIMARY: 'primary',
  SUCCESS: 'success',
  WARNING: 'warning',
  DANGER: 'danger',
  INFO: 'info'
}

// 表单验证规则类型
export const VALIDATION_TYPES = {
  REQUIRED: 'required',
  EMAIL: 'email',
  URL: 'url',
  NUMBER: 'number',
  INTEGER: 'integer',
  FLOAT: 'float',
  PATTERN: 'pattern',
  MIN: 'min',
  MAX: 'max',
  MIN_LENGTH: 'minLength',
  MAX_LENGTH: 'maxLength'
}

/**
 * 合并组件配置的通用函数
 * @param {Object} defaultConfig 默认配置
 * @param {Object} currentConfig 当前表单项配置
 * @param {Object} context 上下文对象 (this)
 * @returns {Object} 合并后的配置
 */
function mergeComponentConfig(defaultConfig, currentConfig, context) {
  const config = {
    props: {
      value: context.value
    },
    on: {
      input(...args) {
        context.$emit('input', ...args)
      }
    }
  }

  Object.keys(defaultConfig).forEach(key => {
    if (typeof defaultConfig[key] === 'object') {
      config[key] = { ...config[key], ...defaultConfig[key] }
    }
  })

  Object.keys(currentConfig).forEach(key => {
    if (typeof currentConfig[key] === 'object') {
      config[key] = { ...config[key], ...currentConfig[key] }
    }
  })

  return config
}

// 预设组件映射配置
export const PRESET_COMPONENT_MAP = {
  [COMPONENT_TYPES.INPUT]: function(h, { item }) {
    const config = mergeComponentConfig({
      attrs: {
        placeholder: '请输入' + item.label,
        clearable: true
      }
    }, item.config || {}, this)

    return h('el-input', config)
  },
  [COMPONENT_TYPES.SELECT]: function(h, { item }) {
    const config = mergeComponentConfig({
      props: {
        placeholder: '请选择' + item.label,
        clearable: true,
        filterable: true
      }
    }, item.config || {}, this)

    // 构建子选项
    const children = item.options?.map(option =>
      h('el-option', {
        props: {
          key: option.value,
          label: option.label,
          value: option.value,
          disabled: option.disabled || false
        }
      })
    ) || []

    return h('el-select', config, children)
  },
  [COMPONENT_TYPES.TEXTAREA]: function(h, { item }) {
    const config = mergeComponentConfig({
      props: {
        type: 'textarea',
        placeholder: '请输入' + item.label,
        rows: 3,
        autosize: { minRows: 2, maxRows: 6 }
      }
    }, item.config || {}, this)

    return h('el-input', config)
  },
  [COMPONENT_TYPES.SWITCH]: function(h, { item }) {
    const config = mergeComponentConfig({
      props: {
        'active-color': '#409EFF',
        'inactive-color': '#C0CCDA'
      }
    }, item.config || {}, this)

    return h('el-switch', config)
  },
  [COMPONENT_TYPES.INPUT_NUMBER]: function(h, { item }) {
    const config = mergeComponentConfig({
      props: {
        placeholder: '请输入数字',
        controls: true,
        precision: 0
      }
    }, item.config || {}, this)

    return h('el-input-number', config)
  },
  [COMPONENT_TYPES.CASCADER]: function(h, { item }) {
    const config = mergeComponentConfig({
      props: {
        placeholder: '请选择' + item.label,
        clearable: true,
        filterable: true,
        'show-all-levels': false,
        options: item.options || []
      }
    }, item.config || {}, this)

    return h('el-cascader', config)
  },
  [COMPONENT_TYPES.TIME_PICKER]: function(h, { item }) {
    const config = mergeComponentConfig({
      props: {
        placeholder: '请选择时间',
        clearable: true,
        format: 'HH:mm:ss',
        'value-format': 'HH:mm:ss'
      }
    }, item.config || {}, this)

    return h('el-time-picker', config)
  },
  [COMPONENT_TYPES.DATE_PICKER]: function(h, { item }) {
    const config = mergeComponentConfig({
      props: {
        placeholder: '请选择日期',
        type: 'date',
        clearable: true,
        format: 'yyyy-MM-dd',
        'value-format': 'yyyy-MM-dd'
      }
    }, item.config || {}, this)

    return h('el-date-picker', config)
  },
  [COMPONENT_TYPES.DATE_RANGE]: function(h, { item }) {
    const config = mergeComponentConfig({
      props: {
        type: 'daterange',
        'range-separator': '至',
        'start-placeholder': '开始日期',
        'end-placeholder': '结束日期',
        clearable: true,
        format: 'yyyy-MM-dd',
        'value-format': 'yyyy-MM-dd'
      }
    }, item.config || {}, this)

    return h('el-date-picker', config)
  },
  [COMPONENT_TYPES.DATETIME_PICKER]: function(h, { item }) {
    const config = mergeComponentConfig({
      props: {
        placeholder: '请选择日期时间',
        type: 'datetime',
        clearable: true,
        format: 'yyyy-MM-dd HH:mm:ss',
        'value-format': 'yyyy-MM-dd HH:mm:ss'
      }
    }, item.config || {}, this)

    return h('el-date-picker', config)
  },
  [COMPONENT_TYPES.RATE]: function(h, { item }) {
    const config = mergeComponentConfig({
      props: {
        max: 5,
        'allow-half': false,
        'show-text': false,
        'show-score': false
      }
    }, item.config || {}, this)

    return h('el-rate', config)
  }
}

// 默认配置
export const DEFAULT_CONFIG = {
  filter: {
    title: '筛选条件',
    showReset: true,
    items: []
  },
  table: {
    title: '数据列表',
    showAdd: true,
    selection: true,
    pagination: true,
    attrs: {
      border: true,
      stripe: true
    },
    columns: []
  },
  dialog: {
    title: '新增',
    editTitle: '编辑',
    width: '600px',
    items: []
  }
}

// 分页默认配置
export const DEFAULT_PAGINATION = {
  page: 1,
  size: 10,
  total: 0,
  pageSizes: [10, 20, 50, 100]
}

