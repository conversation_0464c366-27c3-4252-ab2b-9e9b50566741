/**
 * ConfigurablePage 组件常量定义
 */

// 组件类型枚举
export const COMPONENT_TYPES = {
  // 表单组件类型
  INPUT: 'input',
  INPUT_NUMBER: 'input-number',
  SELECT: 'select',
  RADIO: 'radio',
  CASCADER: 'cascader',
  SWITCH: 'switch',
  TIME_PICKER: 'time-picker',
  DATE_PICKER: 'date-picker',
  DATE_RANGE: 'date-range',
  DATETIME_PICKER: 'datetime-picker',
  RATE: 'rate',
  TEXTAREA: 'textarea',

  // 表格列类型
  TAG: 'tag',
  ACTIONS: 'actions',

  // 自定义渲染
  RENDER: 'render'
}

// 表单组件类型验证数组
export const FORM_COMPONENT_TYPES = [
  COMPONENT_TYPES.INPUT,
  COMPONENT_TYPES.SELECT,
  COMPONENT_TYPES.DATE,
  COMPONENT_TYPES.TEXTAREA,
  COMPONENT_TYPES.SWITCH
]

// 表格列类型验证数组
export const TABLE_COLUMN_TYPES = [
  COMPONENT_TYPES.TAG,
  COMPONENT_TYPES.ACTIONS
]

// 所有支持的组件类型
export const ALL_COMPONENT_TYPES = [
  ...FORM_COMPONENT_TYPES,
  ...TABLE_COLUMN_TYPES,
  COMPONENT_TYPES.RENDER
]

// 按钮类型枚举
export const BUTTON_TYPES = {
  PRIMARY: 'primary',
  SUCCESS: 'success',
  WARNING: 'warning',
  DANGER: 'danger',
  INFO: 'info',
  TEXT: 'text'
}

// 标签类型枚举
export const TAG_TYPES = {
  PRIMARY: 'primary',
  SUCCESS: 'success',
  WARNING: 'warning',
  DANGER: 'danger',
  INFO: 'info'
}

// 表单验证规则类型
export const VALIDATION_TYPES = {
  REQUIRED: 'required',
  EMAIL: 'email',
  URL: 'url',
  NUMBER: 'number',
  INTEGER: 'integer',
  FLOAT: 'float',
  PATTERN: 'pattern',
  MIN: 'min',
  MAX: 'max',
  MIN_LENGTH: 'minLength',
  MAX_LENGTH: 'maxLength'
}

// 预设组件映射配置
export const PRESET_COMPONENT_MAP = {
  [COMPONENT_TYPES.INPUT]: {
    component: 'el-input',
    props: {
      placeholder: '请输入',
      clearable: true
    },
    on: {
      input: function(value) {
        this.$emit('input', value)
      }
    }
  },
  [COMPONENT_TYPES.SELECT]: function(h, { value, item, emit }) {
    // 构建子选项
    const children = item.options?.map(option =>
      h('el-option', {
        props: {
          key: option.value,
          label: option.label,
          value: option.value,
          disabled: option.disabled || false
        }
      })
    ) || []

    // 渲染 el-select 组件
    return h('el-select', {
      props: {
        value,
        placeholder: '请选择',
        clearable: true,
        filterable: true,
        // 合并用户自定义属性
        ...item.config?.props
      },
      on: {
        input: emit,
        // 合并用户自定义事件
        ...item.config?.on
      },
      attrs: item.config?.attrs,
      style: item.config?.style,
      class: item.config?.class,
      scopedSlots: item.config?.scopedSlots
    }, children)
  },
  [COMPONENT_TYPES.TEXTAREA]: {
    component: 'el-input',
    props: {
      type: 'textarea',
      placeholder: '请输入',
      rows: 3,
      autosize: { minRows: 2, maxRows: 6 }
    },
    on: {
      input: function(value) {
        this.$emit('input', value)
      }
    }
  },
  [COMPONENT_TYPES.SWITCH]: {
    component: 'el-switch',
    props: {
      'active-color': '#409EFF',
      'inactive-color': '#C0CCDA'
    },
    on: {
      input: function(value) {
        this.$emit('input', value)
      }
    }
  },
  [COMPONENT_TYPES.INPUT_NUMBER]: {
    component: 'el-input-number',
    props: {
      placeholder: '请输入数字',
      controls: true,
      precision: 0
    },
    on: {
      input: function(value) {
        this.$emit('input', value)
      }
    }
  },
  [COMPONENT_TYPES.CASCADER]: {
    component: 'el-cascader',
    props: {
      placeholder: '请选择',
      clearable: true,
      filterable: true,
      'show-all-levels': false
    },
    on: {
      input: function(value) {
        this.$emit('input', value)
      }
    }
  },
  [COMPONENT_TYPES.TIME_PICKER]: {
    component: 'el-time-picker',
    props: {
      placeholder: '请选择时间',
      clearable: true,
      format: 'HH:mm:ss',
      'value-format': 'HH:mm:ss'
    },
    on: {
      input: function(value) {
        this.$emit('input', value)
      }
    }
  },
  [COMPONENT_TYPES.DATE_PICKER]: {
    component: 'el-date-picker',
    props: {
      placeholder: '请选择日期',
      type: 'date',
      clearable: true,
      format: 'yyyy-MM-dd',
      'value-format': 'yyyy-MM-dd'
    },
    on: {
      input: function(value) {
        this.$emit('input', value)
      }
    }
  },
  [COMPONENT_TYPES.DATE_RANGE]: {
    component: 'el-date-picker',
    props: {
      type: 'daterange',
      'range-separator': '至',
      'start-placeholder': '开始日期',
      'end-placeholder': '结束日期',
      clearable: true,
      format: 'yyyy-MM-dd',
      'value-format': 'yyyy-MM-dd'
    },
    on: {
      input: function(value) {
        this.$emit('input', value)
      }
    }
  },
  [COMPONENT_TYPES.DATETIME_PICKER]: {
    component: 'el-date-picker',
    props: {
      placeholder: '请选择日期时间',
      type: 'datetime',
      clearable: true,
      format: 'yyyy-MM-dd HH:mm:ss',
      'value-format': 'yyyy-MM-dd HH:mm:ss'
    },
    on: {
      input: function(value) {
        this.$emit('input', value)
      }
    }
  },
  [COMPONENT_TYPES.RATE]: {
    component: 'el-rate',
    props: {
      max: 5,
      'allow-half': false,
      'show-text': false,
      'show-score': false
    },
    on: {
      input: function(value) {
        this.$emit('input', value)
      }
    }
  }
}

// 默认配置
export const DEFAULT_CONFIG = {
  filter: {
    title: '筛选条件',
    showReset: true,
    items: []
  },
  table: {
    title: '数据列表',
    showAdd: true,
    selection: true,
    pagination: true,
    attrs: {
      border: true,
      stripe: true
    },
    columns: []
  },
  dialog: {
    title: '新增',
    editTitle: '编辑',
    width: '600px',
    items: []
  }
}

// 分页默认配置
export const DEFAULT_PAGINATION = {
  page: 1,
  size: 10,
  total: 0,
  pageSizes: [10, 20, 50, 100]
}

