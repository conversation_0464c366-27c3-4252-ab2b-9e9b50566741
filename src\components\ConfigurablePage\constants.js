/**
 * ConfigurablePage 组件常量定义
 */

// 组件类型枚举
export const COMPONENT_TYPES = {
  // 表单组件类型
  INPUT: 'input',
  SELECT: 'select',
  DATE: 'date',
  TEXTAREA: 'textarea',
  SWITCH: 'switch',

  // 表格列类型
  TAG: 'tag',
  ACTIONS: 'actions',

  // 自定义渲染
  RENDER: 'render'
}

// 表单组件类型验证数组
export const FORM_COMPONENT_TYPES = [
  COMPONENT_TYPES.INPUT,
  COMPONENT_TYPES.SELECT,
  COMPONENT_TYPES.DATE,
  COMPONENT_TYPES.TEXTAREA,
  COMPONENT_TYPES.SWITCH
]

// 表格列类型验证数组
export const TABLE_COLUMN_TYPES = [
  COMPONENT_TYPES.TAG,
  COMPONENT_TYPES.ACTIONS
]

// 所有支持的组件类型
export const ALL_COMPONENT_TYPES = [
  ...FORM_COMPONENT_TYPES,
  ...TABLE_COLUMN_TYPES,
  COMPONENT_TYPES.RENDER
]

// 按钮类型枚举
export const BUTTON_TYPES = {
  PRIMARY: 'primary',
  SUCCESS: 'success',
  WARNING: 'warning',
  DANGER: 'danger',
  INFO: 'info',
  TEXT: 'text'
}

// 标签类型枚举
export const TAG_TYPES = {
  PRIMARY: 'primary',
  SUCCESS: 'success',
  WARNING: 'warning',
  DANGER: 'danger',
  INFO: 'info'
}

// 表单验证规则类型
export const VALIDATION_TYPES = {
  REQUIRED: 'required',
  EMAIL: 'email',
  URL: 'url',
  NUMBER: 'number',
  INTEGER: 'integer',
  FLOAT: 'float',
  PATTERN: 'pattern',
  MIN: 'min',
  MAX: 'max',
  MIN_LENGTH: 'minLength',
  MAX_LENGTH: 'maxLength'
}

// 预设组件映射配置
export const PRESET_COMPONENT_MAP = {
  [COMPONENT_TYPES.INPUT]: {
    component: 'el-input',
    defaultProps: {
      placeholder: '请输入',
      clearable: true
    },
    supportedEvents: ['input', 'change', 'blur', 'focus', 'clear'],
    supportedSlots: ['prefix', 'suffix', 'prepend', 'append']
  },
  [COMPONENT_TYPES.SELECT]: {
    component: 'el-select',
    defaultProps: {
      placeholder: '请选择',
      clearable: true,
      filterable: true
    },
    supportedEvents: ['change', 'visible-change', 'remove-tag', 'clear', 'blur', 'focus'],
    supportedSlots: ['prefix', 'empty']
  },
  [COMPONENT_TYPES.DATE]: {
    component: 'el-date-picker',
    defaultProps: {
      placeholder: '请选择日期',
      type: 'date',
      clearable: true,
      format: 'yyyy-MM-dd',
      'value-format': 'yyyy-MM-dd'
    },
    supportedEvents: ['change', 'blur', 'focus'],
    supportedSlots: []
  },
  [COMPONENT_TYPES.TEXTAREA]: {
    component: 'el-input',
    defaultProps: {
      type: 'textarea',
      placeholder: '请输入',
      rows: 3,
      autosize: { minRows: 2, maxRows: 6 }
    },
    supportedEvents: ['input', 'change', 'blur', 'focus'],
    supportedSlots: []
  },
  [COMPONENT_TYPES.SWITCH]: {
    component: 'el-switch',
    defaultProps: {
      'active-color': '#409EFF',
      'inactive-color': '#C0CCDA'
    },
    supportedEvents: ['change'],
    supportedSlots: []
  }
}

// 默认配置
export const DEFAULT_CONFIG = {
  filter: {
    title: '筛选条件',
    showReset: true,
    items: []
  },
  table: {
    title: '数据列表',
    showAdd: true,
    selection: true,
    pagination: true,
    attrs: {
      border: true,
      stripe: true
    },
    columns: []
  },
  dialog: {
    title: '新增',
    editTitle: '编辑',
    width: '600px',
    items: []
  }
}

// 分页默认配置
export const DEFAULT_PAGINATION = {
  page: 1,
  size: 10,
  total: 0,
  pageSizes: [10, 20, 50, 100]
}

/**
 * 增强组件配置示例
 *
 * 基本用法：
 * {
 *   prop: 'name',
 *   label: '姓名',
 *   type: 'input',
 *   placeholder: '请输入姓名'
 * }
 *
 * 增强用法（使用 component 字段）：
 * {
 *   prop: 'name',
 *   label: '姓名',
 *   type: 'input',
 *   component: {
 *     props: {
 *       placeholder: '请输入姓名',
 *       clearable: true,
 *       maxlength: 50,
 *       'show-word-limit': true
 *     },
 *     on: {
 *       blur: function(event) {
 *         console.log('输入框失焦', event)
 *       },
 *       clear: function() {
 *         console.log('清空输入框')
 *       }
 *     },
 *     slots: {
 *       prefix: function(h) {
 *         return h('i', { class: 'el-icon-user' })
 *       }
 *     },
 *     style: {
 *       width: '300px'
 *     },
 *     class: 'custom-input'
 *   }
 * }
 *
 * Select 组件示例：
 * {
 *   prop: 'status',
 *   label: '状态',
 *   type: 'select',
 *   component: {
 *     options: [
 *       { label: '启用', value: 1 },
 *       { label: '禁用', value: 0 }
 *     ],
 *     props: {
 *       placeholder: '请选择状态',
 *       clearable: true,
 *       filterable: true
 *     },
 *     on: {
 *       change: function(value) {
 *         console.log('选择变化', value)
 *         // 可以访问组件实例的数据和方法
 *         this.loadData()
 *       }
 *     }
 *   }
 * }
 */
export const ENHANCED_COMPONENT_EXAMPLES = {
  INPUT: {
    prop: 'name',
    label: '姓名',
    type: COMPONENT_TYPES.INPUT,
    component: {
      props: {
        placeholder: '请输入姓名',
        clearable: true,
        maxlength: 50,
        'show-word-limit': true
      },
      on: {
        blur: function(event) {
          console.log('输入框失焦', event)
        }
      }
    }
  },
  SELECT: {
    prop: 'status',
    label: '状态',
    type: COMPONENT_TYPES.SELECT,
    component: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ],
      props: {
        placeholder: '请选择状态',
        clearable: true
      }
    }
  }
}
