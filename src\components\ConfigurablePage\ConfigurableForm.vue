<template>
  <el-form
    :ref="formRef"
    :model="value"
    v-bind="formAttrs"
  >
    <el-form-item
      v-for="(item, index) in config.items"
      :key="item.prop"
      v-bind="getFormItemAttrs(item)"
    >
      <!-- 增强的预设组件 -->
      <preset-component
        v-if="item.type && !item.render"
        v-model="value[item.prop]"
        :type="item.type"
        :item="item"
        :row="value"
        :column="item"
        :index="index"
      />
      <!-- 自定义render函数 -->
      <render-component
        v-else-if="item.render"
        v-model="value[item.prop]"
        :render-func="item.render"
        :item="item"
        :row="value"
        :column="item"
        :index="index"
      />
    </el-form-item>

    <!-- 插槽：用于添加额外的表单项或按钮 -->
    <slot :form-data="value" :items="config.items" />
  </el-form>
</template>

<script>
import PresetComponent from './PresetComponent'
import RenderComponent from './RenderComponent'

export default {
  name: 'ConfigurableForm',
  components: {
    PresetComponent,
    RenderComponent
  },
  props: {
    // 表单配置项
    config: {
      type: Object,
      default: () => ({})
    },
    // 表单数据
    value: {
      type: Object,
      default: () => ({})
    },
    // 表单引用名称
    formRef: {
      type: String,
      default: 'form'
    },
    // 是否内联模式
    inline: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    formAttrs() {
      const attrs = {}
      Object.assign(attrs, { inline: this.inline }, this.config.attrs)

      return attrs
    }
  },
  watch: {
    'config.items': {
      handler() {
        this.initFormData()
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      const newFormData = { ...this.value }

      this.config.items.forEach(item => {
        if (!(item.prop in newFormData)) {
          this.$set(newFormData, item.prop, null)
        }
      })

      this.$emit('input', newFormData)
    },

    getFormItemAttrs(item) {
      // eslint-disable-next-line no-unused-vars
      const { type, render, config, options, ...attrs } = item
      return attrs
    },

    // 验证表单
    validate(callback) {
      return this.$refs[this.formRef].validate(callback)
    },

    // 重置表单
    resetFields() {
      this.$refs[this.formRef].resetFields()
      this.initFormData()
    },

    // 清空验证
    clearValidate(props) {
      this.$refs[this.formRef].clearValidate(props)
    },

    // 验证指定字段
    validateField(props, callback) {
      this.$refs[this.formRef].validateField(props, callback)
    }
  }
}
</script>

<style scoped>
/* 可以添加一些通用的表单样式 */
</style>
