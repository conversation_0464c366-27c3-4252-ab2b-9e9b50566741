<template>
  <el-form
    :ref="formRef"
    :model="formData"
    :rules="rules"
    :inline="inline"
    :label-width="labelWidth"
    v-bind="formAttrs"
  >
    <el-form-item
      v-for="item in items"
      :key="item.prop"
      :label="item.label"
      :prop="item.prop"
      :required="item.required"
      v-bind="getFormItemAttrs(item)"
    >
      <!-- 增强的预设组件 -->
      <preset-component
        v-if="item.type && !item.render"
        :type="item.type"
        :value="formData[item.prop]"
        :item="item"
        :component="item.component"
        :style="getComponentStyle(item)"
        @input="handleInput(item.prop, $event)"
        v-on="getComponentEvents(item)"
      />
      <!-- 自定义render函数 -->
      <render-component
        v-else-if="item.render"
        :render-func="item.render"
        :value="formData[item.prop]"
        :item="item"
        @input="handleInput(item.prop, $event)"
      />
    </el-form-item>

    <!-- 插槽：用于添加额外的表单项或按钮 -->
    <slot :form-data="formData" :items="items" />
  </el-form>
</template>

<script>
import { PresetComponent } from './PresetComponent'
import { RenderComponent } from './RenderComponent'

export default {
  name: 'ConfigurableForm',
  components: {
    PresetComponent,
    RenderComponent
  },
  props: {
    // 表单配置项
    items: {
      type: Array,
      default: () => []
    },
    // 表单数据
    value: {
      type: Object,
      default: () => ({})
    },
    // 表单验证规则
    rules: {
      type: Object,
      default: () => ({})
    },
    // 是否内联显示
    inline: {
      type: Boolean,
      default: false
    },
    // 标签宽度
    labelWidth: {
      type: String,
      default: '100px'
    },
    // 表单引用名称
    formRef: {
      type: String,
      default: 'form'
    },
    // 额外的表单属性
    formAttrs: {
      type: Object,
      default: () => ({})
    },
    // 默认组件宽度（用于筛选表单）
    defaultComponentWidth: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      formData: {}
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.formData = { ...newVal }
      },
      deep: true,
      immediate: true
    },
    items: {
      handler() {
        this.initFormData()
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      const newFormData = { ...this.value }

      this.items.forEach(item => {
        if (!(item.prop in newFormData)) {
          this.$set(newFormData, item.prop, item.defaultValue || '')
        }
      })

      this.formData = newFormData
      this.$emit('input', this.formData)
    },

    // 处理输入事件
    handleInput(prop, value) {
      this.$set(this.formData, prop, value)
      this.$emit('input', this.formData)
      this.$emit('change', { prop, value, formData: this.formData })
    },

    // 获取表单项属性
    getFormItemAttrs(item) {
      const attrs = {}

      // 如果有自定义的表单项属性
      if (item.formItemAttrs) {
        Object.assign(attrs, item.formItemAttrs)
      }

      return attrs
    },

    // 获取组件样式
    getComponentStyle(item) {
      const style = {}

      // 设置组件宽度
      if (item.width) {
        style.width = item.width
      } else if (this.defaultComponentWidth) {
        style.width = this.defaultComponentWidth
      }

      // 合并自定义样式
      if (item.style) {
        Object.assign(style, item.style)
      }

      return style
    },

    // 获取组件事件监听器
    getComponentEvents(item) {
      const events = {}

      // 如果组件配置中有事件定义（使用 on 字段，符合 Vue 2 渲染函数格式）
      if (item.component && item.component.on) {
        Object.keys(item.component.on).forEach(eventName => {
          const handler = item.component.on[eventName]
          if (typeof handler === 'function') {
            events[eventName] = (...args) => {
              // 调用事件处理函数，传入当前组件实例作为上下文
              handler.call(this.$parent, ...args, {
                item,
                formData: this.formData,
                form: this
              })
            }
          }
        })
      }

      return events
    },

    // 验证表单
    validate(callback) {
      return this.$refs[this.formRef].validate(callback)
    },

    // 重置表单
    resetFields() {
      this.$refs[this.formRef].resetFields()
      this.initFormData()
    },

    // 清空验证
    clearValidate(props) {
      this.$refs[this.formRef].clearValidate(props)
    },

    // 验证指定字段
    validateField(props, callback) {
      this.$refs[this.formRef].validateField(props, callback)
    },

    // 获取表单数据
    getFormData() {
      return { ...this.formData }
    },

    // 设置表单数据
    setFormData(data) {
      Object.keys(data).forEach(key => {
        this.$set(this.formData, key, data[key])
      })
      this.$emit('input', this.formData)
    },

    // 设置单个字段值
    setFieldValue(prop, value) {
      this.$set(this.formData, prop, value)
      this.$emit('input', this.formData)
    }
  }
}
</script>

<style scoped>
/* 可以添加一些通用的表单样式 */
</style>
