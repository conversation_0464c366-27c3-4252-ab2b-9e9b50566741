<template>
  <div class="configurable-page">
    <!-- 筛选表单 -->
    <el-card v-if="config.filter" class="filter-card" shadow="never" :body-style="{ padding: '15px 15px 0' }">
      <div v-if="config.filter.title" slot="header" class="clearfix">
        <span>{{ config.filter.title }}</span>
      </div>
      <configurable-form
        ref="filterForm"
        v-model="filterForm"
        :config="config.filter.form"
        :inline="true"
        form-ref="filterForm"
      >
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </configurable-form>
    </el-card>

    <!-- 表格 -->
    <el-card v-if="config.table" class="table-card" shadow="never" :body-style="{ padding: '0 0 15px' }">
      <div v-if="config.table.title" slot="header" class="table-header">
        <span>{{ config.table.title }}</span>
        <div>
          <el-button
            v-if="config.table.showAdd"
            type="primary"
            size="small"
            @click="handleAdd"
          >
            新增
          </el-button>
        </div>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        style="border: none;"
        v-bind="config.table.attrs"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          v-if="config.table.selection"
          type="selection"
          width="55"
        />
        <el-table-column
          v-for="column in config.table.columns"
          :key="column.prop"
          v-bind="column"
        >
          <template slot-scope="scope">
            <!-- 预设列类型 -->
            <span v-if="!column.render && !column.type">
              {{ scope.row[column.prop] }}
            </span>
            <div v-else-if="column.type === 'actions'" class="table-actions">
              <el-button
                v-for="action in column.actions"
                :key="action.name"
                :type="action.type || 'text'"
                :size="action.size || 'small'"
                @click="handleAction(action.name, scope.row, scope.$index)"
              >
                {{ action.label }}
              </el-button>
            </div>
            <!-- 自定义render函数 -->
            <render-component
              v-else-if="column.render"
              :render-func="column.render"
              :row="scope.row"
              :column="column"
              :index="scope.$index"
            />
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-if="config.table.pagination !== false"
        :current-page="pagination.page"
        :page-sizes="pagination.pageSizes"
        :page-size="pagination.size"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        style="margin-top: 20px; text-align: right;"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 修改弹窗表单 -->
    <el-dialog
      v-if="config.dialog"
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      v-bind="config.dialog.attrs"
    >
      <configurable-form
        ref="dialogForm"
        v-model="dialogForm"
        :config="config.dialog.form"
        form-ref="dialogForm"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>

    <!-- 修改抽屉表单 -->
    <el-drawer
      v-if="config.drawer"
      :visible.sync="dialogVisible"
      :custom-class="config.drawer.customClass || 'drawer-form'"
      :with-header="false"
      v-bind="config.drawer.attrs"
    >
      <div class="drawer-content">
        <div class="drawer-header">
          <div class="drawer-header-close" @click="dialogVisible = false">
            <i class="el-icon-close" />
          </div>
          <span>{{ dialogTitle }}</span>
        </div>
        <div class="drawer-body">
          <configurable-form
            ref="dialogForm"
            v-model="dialogForm"
            :config="config.drawer.form"
            form-ref="dialogForm"
          />
        </div>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  COMPONENT_TYPES,
  FORM_COMPONENT_TYPES,
  TABLE_COLUMN_TYPES
} from './constants'
import ConfigurableForm from './ConfigurableForm'
import RenderComponent from './RenderComponent'

export default {
  name: 'ConfigurablePage',
  components: {
    ConfigurableForm,
    RenderComponent
  },
  props: {
    config: {
      type: Object,
      required: true
    },
    // 数据获取函数
    fetchData: {
      type: Function,
      default: null
    },
    // 数据提交函数
    submitData: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      filterForm: {},
      tableData: [],
      selectedRows: [],
      pagination: {
        page: 1,
        size: 10,
        total: 0,
        pageSizes: [10, 20, 50, 100]
      },
      dialogVisible: false,
      dialogForm: {},
      dialogMode: 'add', // add | edit
      currentEditRow: null
    }
  },
  computed: {
    dialogTitle() {
      return this.dialogMode === 'add'
        ? (this.config.dialog?.addTitle || '新增')
        : (this.config.dialog?.editTitle || '编辑')
    }
  },
  watch: {
    config: {
      handler() {
        // ConfigurableForm 组件会自动处理表单初始化
        // 这里只需要重置分页
        this.pagination.page = 1
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    // 验证组件类型
    validateComponentType(item, context = 'form') {
      if (!item.type && !item.render) {
        console.warn(`[ConfigurablePage] ${context} item "${item.prop}" has no type or render function`)
        return false
      }

      if (item.type) {
        const validTypes = context === 'form' ? FORM_COMPONENT_TYPES : TABLE_COLUMN_TYPES
        if (!validTypes.includes(item.type) && item.type !== COMPONENT_TYPES.RENDER) {
          console.warn(`[ConfigurablePage] Invalid ${context} component type "${item.type}". Valid types: ${validTypes.join(', ')}`)
          return false
        }
      }

      return true
    },

    // 加载数据
    async loadData() {
      if (!this.fetchData) return

      this.loading = true
      try {
        const params = {
          ...this.filterForm,
          page: this.pagination.page,
          size: this.pagination.size
        }
        const result = await this.fetchData(params)

        if (result) {
          this.tableData = result.data || result.list || []
          this.pagination.total = result.total || 0
        }
      } catch (error) {
        this.$message.error('数据加载失败')
        console.error(error)
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.page = 1
      this.loadData()
    },

    // 重置筛选条件
    handleReset() {
      this.$refs.filterForm.resetFields()
      this.pagination.page = 1
      this.loadData()
    },

    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
      this.$emit('selection-change', selection)
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.page = 1
      this.loadData()
    },

    // 当前页变化
    handleCurrentChange(page) {
      this.pagination.page = page
      this.loadData()
    },

    // 获取标签类型
    getTagType(value, tagMap) {
      if (!tagMap) return ''
      const item = tagMap.find(item => item.value === value)
      return item ? item.type : ''
    },

    // 处理操作按钮点击
    handleAction(actionName, row, index) {
      if (actionName === 'edit') {
        this.handleEdit(row, index)
      } else if (actionName === 'delete') {
        this.handleDelete(row, index)
      } else {
        this.$emit('action', { action: actionName, row, index })
      }
    },

    // 新增
    handleAdd() {
      this.dialogMode = 'add'
      this.currentEditRow = null
      this.resetDialogForm()
      this.dialogVisible = true
    },

    // 编辑
    handleEdit(row, index) {
      this.dialogMode = 'edit'
      this.currentEditRow = { ...row, _index: index }
      this.fillDialogForm(row)
      this.dialogVisible = true
    },

    // 删除
    handleDelete(row, index) {
      this.$confirm('确定要删除这条记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('delete', { row, index })
      })
    },

    // 重置弹窗表单
    resetDialogForm() {
      if (this.$refs.dialogForm) {
        this.$refs.dialogForm.resetFields()
      }
    },

    // 填充弹窗表单
    fillDialogForm(row) {
      const formData = {}
      if (this.config.dialog?.form?.items) {
        this.config.dialog.form.items.forEach(item => {
          formData[item.prop] = row[item.prop] || item.defaultValue || ''
        })
      }
      this.dialogForm = formData
    },

    // 提交表单
    handleSubmit() {
      this.$refs.dialogForm.validate(async(valid) => {
        if (!valid) return

        try {
          if (this.submitData) {
            const result = await this.submitData({
              mode: this.dialogMode,
              data: this.dialogForm,
              originalData: this.currentEditRow
            })

            if (result !== false) {
              this.$message.success(this.dialogMode === 'add' ? '新增成功' : '修改成功')
              this.dialogVisible = false
              this.loadData()
            }
          } else {
            this.$emit('submit', {
              mode: this.dialogMode,
              data: this.dialogForm,
              originalData: this.currentEditRow
            })
            this.dialogVisible = false
          }
        } catch (error) {
          this.$message.error('操作失败')
          console.error(error)
        }
      })
    },

    // 刷新数据
    refresh() {
      this.loadData()
    },

    // 获取选中行
    getSelectedRows() {
      return this.selectedRows
    },

    // 获取筛选条件
    getFilterData() {
      return { ...this.filterForm }
    }
  }
}
</script>

<style lang="scss" scoped>
.configurable-page {
  .filter-card {
    margin-bottom: 20px;
  }

  .table-card {
    .clearfix::after {
      content: "";
      display: table;
      clear: both;
    }
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .table-actions {
    .el-button + .el-button {
      margin-left: 5px;
    }
  }

  .dialog-footer {
    text-align: right;
  }

  .drawer-form {
    min-width: 800px;
  }

  .drawer-content {
    display: flex;
    flex-direction: column;
    height: 100%;

    .drawer-header {
      display: flex;
      align-items: center;
      font-weight: bold;

      .drawer-header-close {
        margin-right: 10px;
        cursor: pointer;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #000;
        color: #fff;
      }
    }

    .drawer-body {
      flex: 1;
      overflow: auto;
      padding: 20px;
    }

    .dialog-footer {
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 0 20px;
      box-shadow: 0 -1px 0 0 #eee;
    }
  }
}
</style>
