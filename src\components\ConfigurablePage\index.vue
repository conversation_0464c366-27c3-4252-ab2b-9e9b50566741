<template>
  <div class="configurable-page">
    <!-- 筛选表单 -->
    <el-card v-if="config.filter" class="filter-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>{{ config.filter.title || '筛选条件' }}</span>
      </div>
      <el-form
        ref="filterForm"
        :model="filterForm"
        :inline="true"
        :label-width="config.filter.labelWidth || '80px'"
      >
        <el-form-item
          v-for="item in config.filter.items"
          :key="item.prop"
          :label="item.label"
          :prop="item.prop"
        >
          <!-- 预设组件 -->
          <el-input
            v-if="item.type === 'input'"
            v-model="filterForm[item.prop]"
            :placeholder="item.placeholder"
            :style="{ width: item.width || '200px' }"
            v-bind="item.attrs"
          />
          <el-select
            v-else-if="item.type === 'select'"
            v-model="filterForm[item.prop]"
            :placeholder="item.placeholder"
            :style="{ width: item.width || '200px' }"
            v-bind="item.attrs"
          >
            <el-option
              v-for="option in item.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
          <el-date-picker
            v-else-if="item.type === 'date'"
            v-model="filterForm[item.prop]"
            :type="item.dateType || 'date'"
            :placeholder="item.placeholder"
            :style="{ width: item.width || '200px' }"
            v-bind="item.attrs"
          />
          <!-- 自定义render函数 -->
          <render-component
            v-else-if="item.render"
            :render-func="item.render"
            :value="filterForm[item.prop]"
            :item="item"
            @input="handleFilterInput(item.prop, $event)"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格 -->
    <el-card v-if="config.table" class="table-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>{{ config.table.title || '数据列表' }}</span>
        <div style="float: right;">
          <el-button
            v-if="config.table.showAdd"
            type="primary"
            size="small"
            @click="handleAdd"
          >
            新增
          </el-button>
        </div>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        v-bind="config.table.attrs"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          v-if="config.table.selection"
          type="selection"
          width="55"
        />
        <el-table-column
          v-for="column in config.table.columns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :fixed="column.fixed"
          :sortable="column.sortable"
          :show-overflow-tooltip="column.showOverflowTooltip"
        >
          <template slot-scope="scope">
            <!-- 预设列类型 -->
            <span v-if="!column.render && !column.type">
              {{ scope.row[column.prop] }}
            </span>
            <el-tag
              v-else-if="column.type === 'tag'"
              :type="getTagType(scope.row[column.prop], column.tagMap)"
            >
              {{ getTagText(scope.row[column.prop], column.tagMap) }}
            </el-tag>
            <div v-else-if="column.type === 'actions'" class="table-actions">
              <el-button
                v-for="action in column.actions"
                :key="action.name"
                :type="action.type || 'text'"
                :size="action.size || 'small'"
                @click="handleAction(action.name, scope.row, scope.$index)"
              >
                {{ action.label }}
              </el-button>
            </div>
            <!-- 自定义render函数 -->
            <render-component
              v-else-if="column.render"
              :render-func="column.render"
              :row="scope.row"
              :column="column"
              :index="scope.$index"
            />
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-if="config.table.pagination !== false"
        :current-page="pagination.page"
        :page-sizes="pagination.pageSizes"
        :page-size="pagination.size"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        style="margin-top: 20px; text-align: right;"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 修改弹窗表单 -->
    <el-dialog
      v-if="config.dialog"
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :width="config.dialog.width || '600px'"
      v-bind="config.dialog.attrs"
    >
      <el-form
        ref="dialogForm"
        :model="dialogForm"
        :rules="config.dialog.rules"
        :label-width="config.dialog.labelWidth || '100px'"
      >
        <el-form-item
          v-for="item in config.dialog.items"
          :key="item.prop"
          :label="item.label"
          :prop="item.prop"
          :required="item.required"
        >
          <!-- 预设组件 -->
          <el-input
            v-if="item.type === 'input'"
            v-model="dialogForm[item.prop]"
            :placeholder="item.placeholder"
            :type="item.inputType || 'text'"
            v-bind="item.attrs"
          />
          <el-input
            v-else-if="item.type === 'textarea'"
            v-model="dialogForm[item.prop]"
            type="textarea"
            :placeholder="item.placeholder"
            :rows="item.rows || 3"
            v-bind="item.attrs"
          />
          <el-select
            v-else-if="item.type === 'select'"
            v-model="dialogForm[item.prop]"
            :placeholder="item.placeholder"
            v-bind="item.attrs"
          >
            <el-option
              v-for="option in item.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
          <el-date-picker
            v-else-if="item.type === 'date'"
            v-model="dialogForm[item.prop]"
            :type="item.dateType || 'date'"
            :placeholder="item.placeholder"
            v-bind="item.attrs"
          />
          <el-switch
            v-else-if="item.type === 'switch'"
            v-model="dialogForm[item.prop]"
            v-bind="item.attrs"
          />
          <!-- 自定义render函数 -->
          <render-component
            v-else-if="item.render"
            :render-func="item.render"
            :value="dialogForm[item.prop]"
            :item="item"
            @input="handleDialogInput(item.prop, $event)"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  COMPONENT_TYPES,
  FORM_COMPONENT_TYPES,
  TABLE_COLUMN_TYPES
} from './constants'

export default {
  name: 'ConfigurablePage',
  components: {
    // 动态渲染组件
    RenderComponent: {
      props: {
        renderFunc: Function,
        value: null,
        item: Object,
        row: Object,
        column: Object,
        index: Number
      },
      render(h) {
        const { renderFunc, ...props } = this.$props

        // 检查renderFunc是否为普通函数（非箭头函数）
        if (typeof renderFunc !== 'function') {
          throw new Error('renderFunc must be a function')
        }

        // 检查是否为箭头函数（箭头函数没有prototype属性）
        if (!renderFunc.prototype) {
          throw new Error('renderFunc must be a regular function, not an arrow function. Use "render(h, props) {}" instead of "render: (h, props) => {}"')
        }

        // 将当前组件实例作为context，让render函数可以访问完整的this
        return renderFunc.call(this, h, props)
      }
    }
  },
  props: {
    config: {
      type: Object,
      required: true
    },
    // 数据获取函数
    fetchData: {
      type: Function,
      default: null
    },
    // 数据提交函数
    submitData: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      filterForm: {},
      tableData: [],
      selectedRows: [],
      pagination: {
        page: 1,
        size: 10,
        total: 0,
        pageSizes: [10, 20, 50, 100]
      },
      dialogVisible: false,
      dialogForm: {},
      dialogMode: 'add', // add | edit
      currentEditRow: null
    }
  },
  computed: {
    dialogTitle() {
      return this.dialogMode === 'add'
        ? (this.config.dialog?.addTitle || '新增')
        : (this.config.dialog?.editTitle || '编辑')
    }
  },
  mounted() {
    this.initFilterForm()
    this.initDialogForm()
    this.loadData()
  },
  methods: {
    // 验证组件类型
    validateComponentType(item, context = 'form') {
      if (!item.type && !item.render) {
        console.warn(`[ConfigurablePage] ${context} item "${item.prop}" has no type or render function`)
        return false
      }

      if (item.type) {
        const validTypes = context === 'form' ? FORM_COMPONENT_TYPES : TABLE_COLUMN_TYPES
        if (!validTypes.includes(item.type) && item.type !== COMPONENT_TYPES.RENDER) {
          console.warn(`[ConfigurablePage] Invalid ${context} component type "${item.type}". Valid types: ${validTypes.join(', ')}`)
          return false
        }
      }

      return true
    },

    // 初始化筛选表单
    initFilterForm() {
      if (this.config.filter?.items) {
        this.config.filter.items.forEach(item => {
          // 验证组件类型
          this.validateComponentType(item, 'form')
          this.$set(this.filterForm, item.prop, item.defaultValue || '')
        })
      }
    },

    // 初始化弹窗表单
    initDialogForm() {
      if (this.config.dialog?.items) {
        this.config.dialog.items.forEach(item => {
          // 验证组件类型
          this.validateComponentType(item, 'dialog')
          this.$set(this.dialogForm, item.prop, item.defaultValue || '')
        })
      }
    },

    // 加载数据
    async loadData() {
      if (!this.fetchData) return

      this.loading = true
      try {
        const params = {
          ...this.filterForm,
          page: this.pagination.page,
          size: this.pagination.size
        }
        const result = await this.fetchData(params)

        if (result) {
          this.tableData = result.data || result.list || []
          this.pagination.total = result.total || 0
        }
      } catch (error) {
        this.$message.error('数据加载失败')
        console.error(error)
      } finally {
        this.loading = false
      }
    },

    // 筛选表单输入处理
    handleFilterInput(prop, value) {
      this.$set(this.filterForm, prop, value)
    },

    // 弹窗表单输入处理
    handleDialogInput(prop, value) {
      this.$set(this.dialogForm, prop, value)
    },

    // 搜索
    handleSearch() {
      this.pagination.page = 1
      this.loadData()
    },

    // 重置筛选条件
    handleReset() {
      this.$refs.filterForm.resetFields()
      this.initFilterForm()
      this.pagination.page = 1
      this.loadData()
    },

    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
      this.$emit('selection-change', selection)
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.page = 1
      this.loadData()
    },

    // 当前页变化
    handleCurrentChange(page) {
      this.pagination.page = page
      this.loadData()
    },

    // 获取标签类型
    getTagType(value, tagMap) {
      if (!tagMap) return ''
      const item = tagMap.find(item => item.value === value)
      return item ? item.type : ''
    },

    // 获取标签文本
    getTagText(value, tagMap) {
      if (!tagMap) return value
      const item = tagMap.find(item => item.value === value)
      return item ? item.label : value
    },

    // 处理操作按钮点击
    handleAction(actionName, row, index) {
      if (actionName === 'edit') {
        this.handleEdit(row, index)
      } else if (actionName === 'delete') {
        this.handleDelete(row, index)
      } else {
        this.$emit('action', { action: actionName, row, index })
      }
    },

    // 新增
    handleAdd() {
      this.dialogMode = 'add'
      this.currentEditRow = null
      this.resetDialogForm()
      this.dialogVisible = true
    },

    // 编辑
    handleEdit(row, index) {
      this.dialogMode = 'edit'
      this.currentEditRow = { ...row, _index: index }
      this.fillDialogForm(row)
      this.dialogVisible = true
    },

    // 删除
    handleDelete(row, index) {
      this.$confirm('确定要删除这条记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('delete', { row, index })
      })
    },

    // 重置弹窗表单
    resetDialogForm() {
      if (this.$refs.dialogForm) {
        this.$refs.dialogForm.resetFields()
      }
      this.initDialogForm()
    },

    // 填充弹窗表单
    fillDialogForm(row) {
      if (this.config.dialog?.items) {
        this.config.dialog.items.forEach(item => {
          this.$set(this.dialogForm, item.prop, row[item.prop] || item.defaultValue || '')
        })
      }
    },

    // 提交表单
    handleSubmit() {
      this.$refs.dialogForm.validate(async(valid) => {
        if (!valid) return

        try {
          if (this.submitData) {
            const result = await this.submitData({
              mode: this.dialogMode,
              data: this.dialogForm,
              originalData: this.currentEditRow
            })

            if (result !== false) {
              this.$message.success(this.dialogMode === 'add' ? '新增成功' : '修改成功')
              this.dialogVisible = false
              this.loadData()
            }
          } else {
            this.$emit('submit', {
              mode: this.dialogMode,
              data: this.dialogForm,
              originalData: this.currentEditRow
            })
            this.dialogVisible = false
          }
        } catch (error) {
          this.$message.error('操作失败')
          console.error(error)
        }
      })
    },

    // 刷新数据
    refresh() {
      this.loadData()
    },

    // 获取选中行
    getSelectedRows() {
      return this.selectedRows
    },

    // 获取筛选条件
    getFilterData() {
      return { ...this.filterForm }
    }
  }
}
</script>

<style lang="scss" scoped>
.configurable-page {
  .filter-card {
    margin-bottom: 20px;
  }

  .table-card {
    .clearfix::after {
      content: "";
      display: table;
      clear: both;
    }
  }

  .table-actions {
    .el-button + .el-button {
      margin-left: 5px;
    }
  }

  .dialog-footer {
    text-align: right;
  }
}
</style>
