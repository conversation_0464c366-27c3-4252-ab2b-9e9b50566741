/**
 * 增强的预设组件渲染器
 * 支持完整的参数传递、事件传递和插槽处理
 */

import { PRESET_COMPONENT_MAP } from './constants'

/**
 * 预设组件渲染器
 * 提供类似UI组件库二次封装的完整功能
 */
export const PresetComponent = {
  name: 'PresetComponent',
  props: {
    // 组件类型
    type: {
      type: String,
      required: true
    },
    // 组件值
    value: null,
    // 组件配置项
    item: {
      type: Object,
      default: () => ({})
    },
    // 组件完整配置（新增）
    component: {
      type: Object,
      default: () => ({})
    }
  },
  render(h) {
    const { type, value, item, component } = this

    // 获取预设组件配置
    const presetConfig = PRESET_COMPONENT_MAP[type]
    if (!presetConfig) {
      console.warn(`[PresetComponent] Unknown component type: ${type}`)
      return h('span', '未知组件类型')
    }

    // 合并组件配置
    const finalConfig = this.mergeComponentConfig(presetConfig, item, component)

    // 构建组件属性
    const componentProps = this.buildComponentProps(finalConfig, value)

    // 构建事件监听器
    const componentListeners = this.buildComponentListeners(finalConfig)

    // 构建插槽内容
    const slots = this.buildSlots(finalConfig, h)

    // 渲染组件
    return h(finalConfig.component, {
      props: componentProps,
      on: componentListeners,
      attrs: finalConfig.attrs || {},
      style: finalConfig.style || {},
      class: finalConfig.class || {}
    }, slots)
  },
  methods: {
    /**
     * 合并组件配置
     * 优先级：component > item > presetConfig
     */
    mergeComponentConfig(presetConfig, item, component) {
      const config = {
        component: presetConfig.component,
        defaultProps: { ...presetConfig.defaultProps },
        supportedEvents: [...presetConfig.supportedEvents],
        supportedSlots: [...presetConfig.supportedSlots]
      }

      // 合并 item 中的配置
      if (item.component) {
        Object.assign(config, item.component)
      }

      // 合并 component 配置（最高优先级）
      if (component && Object.keys(component).length > 0) {
        Object.assign(config, component)
      }

      return config
    },

    /**
     * 构建组件属性
     */
    buildComponentProps(config, value) {
      const props = { ...config.defaultProps }

      // 添加 value 属性
      if (value !== undefined && value !== null) {
        props.value = value
      }

      // 合并自定义属性
      if (config.props) {
        Object.assign(props, config.props)
      }

      // 处理特殊属性（如 el-select 的 options）
      if (config.options && Array.isArray(config.options)) {
        // 对于 select 组件，options 会在插槽中处理
        props._options = config.options
      }

      return props
    },

    /**
     * 构建事件监听器
     */
    buildComponentListeners(config) {
      const listeners = {}

      // 添加 input 事件（用于 v-model）
      listeners.input = (value) => {
        this.$emit('input', value)
      }

      // 添加支持的事件
      if (config.supportedEvents) {
        config.supportedEvents.forEach(eventName => {
          if (eventName !== 'input') { // input 事件已经处理
            listeners[eventName] = (...args) => {
              this.$emit(eventName, ...args)
            }
          }
        })
      }

      // 添加自定义事件
      if (config.events) {
        Object.keys(config.events).forEach(eventName => {
          const handler = config.events[eventName]
          if (typeof handler === 'function') {
            listeners[eventName] = (...args) => {
              // 调用自定义处理函数，并传入组件实例上下文
              handler.call(this, ...args)
            }
          }
        })
      }

      return listeners
    },

    /**
     * 构建插槽内容
     */
    buildSlots(config, h) {
      const slots = []

      // 处理 select 组件的 options
      if (config.component === 'el-select' && config.options) {
        config.options.forEach(option => {
          slots.push(h('el-option', {
            props: {
              key: option.value,
              label: option.label,
              value: option.value,
              disabled: option.disabled || false
            }
          }))
        })
      }

      // 处理自定义插槽
      if (config.slots) {
        Object.keys(config.slots).forEach(slotName => {
          const slotContent = config.slots[slotName]

          if (typeof slotContent === 'function') {
            // 如果是函数，调用函数获取插槽内容
            const slotVNode = slotContent.call(this, h, {
              value: this.value,
              item: this.item
            })
            if (slotVNode) {
              slots.push(h('template', { slot: slotName }, [slotVNode]))
            }
          } else if (typeof slotContent === 'string') {
            // 如果是字符串，直接作为文本内容
            slots.push(h('template', { slot: slotName }, slotContent))
          } else if (slotContent && typeof slotContent === 'object') {
            // 如果是对象，作为 VNode 配置
            slots.push(h('template', { slot: slotName }, [h(slotContent)]))
          }
        })
      }

      return slots
    }
  }
}

/**
 * 工具函数：创建预设组件配置
 */
export function createPresetComponentConfig(type, options = {}) {
  const baseConfig = {
    type,
    component: {}
  }

  // 合并配置选项
  if (options.props) {
    baseConfig.component.props = options.props
  }

  if (options.events) {
    baseConfig.component.events = options.events
  }

  if (options.slots) {
    baseConfig.component.slots = options.slots
  }

  if (options.attrs) {
    baseConfig.component.attrs = options.attrs
  }

  if (options.style) {
    baseConfig.component.style = options.style
  }

  if (options.class) {
    baseConfig.component.class = options.class
  }

  // 特殊处理 select 组件的 options
  if (type === 'select' && options.options) {
    baseConfig.component.options = options.options
  }

  return baseConfig
}

/**
 * 工具函数：创建简单的预设组件配置
 */
export function createSimplePresetConfig(type, props = {}, events = {}) {
  return createPresetComponentConfig(type, {
    props,
    events
  })
}
