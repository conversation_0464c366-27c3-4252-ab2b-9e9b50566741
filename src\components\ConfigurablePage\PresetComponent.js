/**
 * 增强的预设组件渲染器
 * 支持完整的参数传递、事件传递和插槽处理
 */

import { PRESET_COMPONENT_MAP } from './constants'

/**
 * 预设组件渲染器
 * 提供类似UI组件库二次封装的完整功能
 */
export const PresetComponent = {
  name: 'PresetComponent',
  props: {
    // 组件类型
    type: {
      type: String,
      required: true
    },
    // 组件值
    value: {
      required: false
    },
    // 表单项配置
    item: {
      type: Object,
      default: () => ({})
    }
  },
  render(h) {
    const { type, value, item } = this

    // 获取预设组件配置
    const presetConfig = PRESET_COMPONENT_MAP[type]
    if (!presetConfig) {
      console.warn(`[PresetComponent] Unknown component type: ${type}`)
      return h('span', '未知组件类型')
    }

    // 构建最终的组件配置（对应 Vue 2 h 函数的第二个参数）
    const componentOptions = this.buildComponentOptions(presetConfig, value, item)

    // 构建插槽内容
    const slots = this.buildSlots(item, h)

    // 渲染组件
    return h(presetConfig.component, componentOptions, slots)
  },
  methods: {
    /**
     * 构建组件配置选项（对应 Vue 2 h 函数的第二个参数）
     */
    buildComponentOptions(presetConfig, value, item) {
      // 从预设配置开始
      const options = {
        props: { ...presetConfig.props },
        on: {},
        attrs: {},
        style: {},
        class: {}
      }

      // 设置 value 属性
      if (value !== undefined && value !== null) {
        options.props.value = value
      }

      // 默认的 input 事件（用于 v-model）
      options.on.input = (value) => {
        this.$emit('input', value)
      }

      // 合并 item.options 配置（对应 Vue 2 h 函数第二个参数格式）
      if (item.options) {
        // 合并 props
        if (item.options.props) {
          Object.assign(options.props, item.options.props)
        }

        // 合并事件处理器
        if (item.options.on) {
          Object.keys(item.options.on).forEach(eventName => {
            const handler = item.options.on[eventName]
            if (typeof handler === 'function') {
              const originalHandler = options.on[eventName]
              options.on[eventName] = (...args) => {
                if (originalHandler) originalHandler(...args)
                handler.call(this, ...args)
              }
            }
          })
        }

        // 合并其他属性
        if (item.options.attrs) {
          Object.assign(options.attrs, item.options.attrs)
        }
        if (item.options.style) {
          Object.assign(options.style, item.options.style)
        }
        if (item.options.class) {
          options.class = item.options.class
        }
      }

      return options
    },

    /**
     * 构建插槽内容
     */
    buildSlots(item, h) {
      const slots = []

      // 处理 select 组件的 options（从 item 中获取）
      if (item.options && Array.isArray(item.options)) {
        item.options.forEach(option => {
          slots.push(h('el-option', {
            props: {
              key: option.value,
              label: option.label,
              value: option.value,
              disabled: option.disabled || false
            }
          }))
        })
      }

      // 处理 radio 组件的 options
      if (item.options && Array.isArray(item.options) && this.type === 'radio') {
        item.options.forEach(option => {
          slots.push(h('el-radio', {
            props: {
              key: option.value,
              label: option.value
            }
          }, option.label))
        })
      }

      // 处理自定义插槽（从 item.options.slots 中获取）
      if (item.options && item.options.slots) {
        Object.keys(item.options.slots).forEach(slotName => {
          const slotContent = item.options.slots[slotName]

          if (typeof slotContent === 'function') {
            // 如果是函数，调用函数获取插槽内容
            const slotVNode = slotContent.call(this, h, {
              value: this.value,
              item: this.item
            })
            if (slotVNode) {
              slots.push(h('template', { slot: slotName }, [slotVNode]))
            }
          } else if (typeof slotContent === 'string') {
            // 如果是字符串，直接作为文本内容
            slots.push(h('template', { slot: slotName }, slotContent))
          } else if (slotContent && typeof slotContent === 'object') {
            // 如果是对象，作为 VNode 配置
            slots.push(h('template', { slot: slotName }, [h(slotContent)]))
          }
        })
      }

      return slots
    }
  }
}

/**
 * 工具函数：创建预设组件配置（新格式）
 */
export function createPresetComponentConfig(type, options = {}) {
  const baseConfig = {
    type,
    options: { ...options }
  }

  return baseConfig
}

/**
 * 工具函数：创建简单的预设组件配置
 */
export function createSimplePresetConfig(type, props = {}, events = {}) {
  return {
    type,
    options: {
      props,
      on: events
    }
  }
}
