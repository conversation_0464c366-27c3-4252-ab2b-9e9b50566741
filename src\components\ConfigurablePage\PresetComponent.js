/**
 * 增强的预设组件渲染器
 * 支持完整的参数传递、事件传递和插槽处理
 */

import { PRESET_COMPONENT_MAP } from './constants'

/**
 * 预设组件渲染器
 */
export const PresetComponent = {
  name: 'PresetComponent',
  props: {
    // 组件类型
    type: {
      type: String,
      required: true
    },
    // 组件值
    value: {
      required: false
    },
    // 表单项配置
    item: {
      type: Object,
      default: () => ({})
    }
  },
  render(h) {
    const { type, value, item } = this

    // 获取预设组件配置
    const presetConfig = PRESET_COMPONENT_MAP[type]
    if (!presetConfig) {
      console.warn(`[PresetComponent] Unknown component type: ${type}`)
      return h('span', '未知组件类型')
    }

    // 构建最终的组件配置（对应 Vue 2 h 函数的第二个参数）
    const componentOptions = this.buildComponentOptions(presetConfig, value, item)

    // 构建子元素（插槽内容）
    const children = this.buildSlots(item, h)

    // 渲染组件
    return h(presetConfig.component, componentOptions, children)
  },
  methods: {
    /**
     * 构建组件配置选项（对应 Vue 2 h 函数的第二个参数）
     */
    buildComponentOptions(presetConfig, value, item) {
      // 从预设配置开始
      const options = {
        props: { ...presetConfig.props },
        on: {},
        attrs: {},
        style: {},
        class: {}
      }

      // 设置 value 属性
      if (value !== undefined && value !== null) {
        options.props.value = value
      }

      // 默认的 input 事件（用于 v-model）- 只有在没有预设时才添加
      if (!options.on.input) {
        options.on.input = (value) => {
          this.$emit('input', value)
        }
      }

      // 合并 item.config 配置（对应 Vue 2 h 函数第二个参数格式）
      if (item.config) {
        // 合并 props
        if (item.config.props) {
          Object.assign(options.props, item.config.props)
        }

        // 合并事件处理器
        if (item.config.on) {
          Object.keys(item.config.on).forEach(eventName => {
            const handler = item.config.on[eventName]
            if (typeof handler === 'function') {
              const originalHandler = options.on[eventName]
              options.on[eventName] = (...args) => {
                if (originalHandler) originalHandler(...args)
                handler.call(this, ...args)
              }
            }
          })
        }

        // 合并其他属性
        if (item.config.attrs) {
          Object.assign(options.attrs, item.config.attrs)
        }
        if (item.config.style) {
          Object.assign(options.style, item.config.style)
        }
        if (item.config.class) {
          options.class = item.config.class
        }
      }

      // 处理自定义插槽（通过 scopedSlots）
      if (item.config && item.config.scopedSlots) {
        options.scopedSlots = { ...item.config.scopedSlots }
      }

      return options
    },

    /**
     * 构建子元素（主要用于 select、radio 等组件的选项）
     */
    buildSlots(item, h) {
      const children = []

      // 处理 select 组件的 options
      if (this.type === 'select' && item.options && Array.isArray(item.options)) {
        item.options.forEach(option => {
          children.push(h('el-option', {
            props: {
              key: option.value,
              label: option.label,
              value: option.value,
              disabled: option.disabled || false
            }
          }))
        })
      }

      // 处理 radio 组件的 options
      if (this.type === 'radio' && item.options && Array.isArray(item.options)) {
        item.options.forEach(option => {
          children.push(h('el-radio', {
            props: {
              key: option.value,
              label: option.value
            }
          }, option.label))
        })
      }

      return children
    }
  }
}

/**
 * 工具函数：深拷贝
 */
function deepClone(source) {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'deepClone')
  }
  const targetObj = source.constructor === Array ? [] : {}
  Object.keys(source).forEach(keys => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys])
    } else {
      targetObj[keys] = source[keys]
    }
    return targetObj
  })
}
