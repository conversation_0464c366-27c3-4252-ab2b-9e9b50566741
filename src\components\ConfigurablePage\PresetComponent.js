/**
 * 增强的预设组件渲染器
 * 支持完整的参数传递、事件传递和插槽处理
 */

import { PRESET_COMPONENT_MAP } from './constants'

/**
 * 预设组件渲染器
 */
export const PresetComponent = {
  name: 'PresetComponent',
  props: {
    type: {
      type: String,
      required: true
    },
    value: null,
    item: Object,
    row: Object,
    column: Object,
    index: Number
  },
  render(h) {
    const { type } = this

    // 获取预设组件配置
    const renderFunc = PRESET_COMPONENT_MAP[type]
    if (!renderFunc) {
      console.warn(`[PresetComponent] Unknown component type: ${type}`)
      return h('span', '未知组件类型')
    }

    // 调用预设组件函数
    return renderFunc.call(this, h, this.$props)
  }
}
