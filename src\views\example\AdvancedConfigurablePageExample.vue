<template>
  <div class="advanced-configurable-page-example">
    <configurable-page
      ref="configurablePage"
      :config="pageConfig"
      :fetch-data="fetchData"
      :submit-data="submitData"
      @action="handleAction"
      @delete="handleDelete"
      @selection-change="handleSelectionChange"
    />

    <!-- 批量操作按钮 -->
    <div v-if="selectedRows.length > 0" class="batch-actions">
      <el-button type="danger" @click="handleBatchDelete">
        批量删除 ({{ selectedRows.length }})
      </el-button>
      <el-button type="warning" @click="handleBatchExport">
        批量导出
      </el-button>
    </div>
  </div>
</template>

<script>
import ConfigurablePage from '@/components/ConfigurablePage'

export default {
  name: 'AdvancedConfigurablePageExample',
  components: {
    ConfigurablePage
  },
  data() {
    return {
      selectedRows: [],
      pageConfig: {
        // 高级筛选表单配置
        filter: {
          title: '',
          labelWidth: '',
          items: [
            {
              prop: 'keyword',
              label: '关键词',
              type: 'input',
              placeholder: '商品名称/编码',
              width: '200px'
            },
            {
              prop: 'category',
              label: '分类',
              type: 'select',
              placeholder: '请选择分类',
              width: '150px',
              options: [
                { label: '全部', value: '' },
                { label: '电子产品', value: 'electronics' },
                { label: '服装', value: 'clothing' },
                { label: '食品', value: 'food' }
              ]
            },
            {
              prop: 'priceRange',
              label: '价格范围',
              render(h, { value }) {
                return h('div', { style: 'display: flex; align-items: center;' }, [
                  h('el-input-number', {
                    props: {
                      value: value ? value[0] : null,
                      placeholder: '最低价',
                      min: 0,
                      precision: 2,
                      size: 'small'
                    },
                    style: { width: '120px' },
                    on: {
                      input: (val) => {
                        const range = value || [null, null]
                        range[0] = val
                        this.$emit('input', range)
                      }
                    }
                  }),
                  h('span', { style: 'margin: 0 10px;' }, '至'),
                  h('el-input-number', {
                    props: {
                      value: value ? value[1] : null,
                      placeholder: '最高价',
                      min: 0,
                      precision: 2,
                      size: 'small'
                    },
                    style: { width: '120px' },
                    on: {
                      input: (val) => {
                        const range = value || [null, null]
                        range[1] = val
                        this.$emit('input', range)
                      }
                    }
                  })
                ])
              }
            },
            {
              prop: 'status',
              label: '状态',
              type: 'select',
              placeholder: '请选择状态',
              width: '120px',
              options: [
                { label: '全部', value: '' },
                { label: '上架', value: 1 },
                { label: '下架', value: 0 },
                { label: '缺货', value: 2 }
              ]
            }
          ]
        },

        // 高级表格配置
        table: {
          title: '商品列表',
          showAdd: true,
          selection: true,
          attrs: {
            border: true,
            stripe: true,
            'highlight-current-row': true
          },
          columns: [
            {
              prop: 'id',
              label: 'ID',
              width: '80',
              sortable: true
            },
            {
              prop: 'image',
              label: '商品图片',
              width: '100',
              render: (h, { row }) => {
                return h('el-image', {
                  props: {
                    src: row.image,
                    fit: 'cover'
                  },
                  style: { width: '60px', height: '60px' },
                  attrs: {
                    'preview-src-list': [row.image]
                  }
                })
              }
            },
            {
              prop: 'name',
              label: '商品名称',
              minWidth: '150',
              showOverflowTooltip: true
            },
            {
              prop: 'code',
              label: '商品编码',
              width: '120'
            },
            {
              prop: 'category',
              label: '分类',
              width: '100',
              render: (h, { row }) => {
                const categoryMap = {
                  'electronics': '电子产品',
                  'clothing': '服装',
                  'food': '食品'
                }
                return h('span', categoryMap[row.category] || row.category)
              }
            },
            {
              prop: 'price',
              label: '价格',
              width: '100',
              sortable: true,
              render: (h, { row }) => {
                return h('span', {
                  style: { color: '#f56c6c', fontWeight: 'bold' }
                }, `¥${row.price}`)
              }
            },
            {
              prop: 'stock',
              label: '库存',
              width: '80',
              render: (h, { row }) => {
                const color = row.stock > 10 ? '#67c23a' : row.stock > 0 ? '#e6a23c' : '#f56c6c'
                return h('span', { style: { color }}, row.stock)
              }
            },
            {
              prop: 'status',
              label: '状态',
              width: '100',
              type: 'tag',
              tagMap: [
                { value: 1, label: '上架', type: 'success' },
                { value: 0, label: '下架', type: 'info' },
                { value: 2, label: '缺货', type: 'warning' }
              ]
            },
            {
              prop: 'createTime',
              label: '创建时间',
              width: '160',
              sortable: true
            },
            {
              prop: 'actions',
              label: '操作',
              width: '300',
              fixed: 'right',
              type: 'actions',
              actions: [
                { name: 'edit', label: '编辑', type: 'primary', size: 'mini' },
                { name: 'copy', label: '复制', type: 'success', size: 'mini' },
                { name: 'toggle', label: '上/下架', type: 'warning', size: 'mini' },
                { name: 'delete', label: '删除', type: 'danger', size: 'mini' }
              ]
            }
          ]
        },

        // 高级弹窗表单配置
        dialog: {
          width: '800px',
          addTitle: '新增商品',
          editTitle: '编辑商品',
          labelWidth: '100px',
          rules: {
            name: [
              { required: true, message: '请输入商品名称', trigger: 'blur' }
            ],
            code: [
              { required: true, message: '请输入商品编码', trigger: 'blur' }
            ],
            category: [
              { required: true, message: '请选择分类', trigger: 'change' }
            ],
            price: [
              { required: true, message: '请输入价格', trigger: 'blur' }
            ]
          },
          items: [
            {
              prop: 'name',
              label: '商品名称',
              type: 'input',
              placeholder: '请输入商品名称',
              required: true
            },
            {
              prop: 'code',
              label: '商品编码',
              type: 'input',
              placeholder: '请输入商品编码',
              required: true
            },
            {
              prop: 'category',
              label: '分类',
              type: 'select',
              placeholder: '请选择分类',
              required: true,
              options: [
                { label: '电子产品', value: 'electronics' },
                { label: '服装', value: 'clothing' },
                { label: '食品', value: 'food' }
              ]
            },
            {
              prop: 'price',
              label: '价格',
              render(h, { value }) {
                return h('el-input-number', {
                  props: {
                    value: value,
                    placeholder: '请输入价格',
                    min: 0,
                    precision: 2,
                    step: 0.01
                  },
                  style: { width: '100%' },
                  on: {
                    input: (val) => {
                      this.$emit('input', val)
                    }
                  }
                })
              }
            },
            {
              prop: 'stock',
              label: '库存',
              render(h, { value }) {
                return h('el-input-number', {
                  props: {
                    value: value,
                    placeholder: '请输入库存',
                    min: 0,
                    step: 1
                  },
                  style: { width: '100%' },
                  on: {
                    input: (val) => {
                      this.$emit('input', val)
                    }
                  }
                })
              }
            },
            {
              prop: 'status',
              label: '状态',
              type: 'select',
              placeholder: '请选择状态',
              defaultValue: 1,
              options: [
                { label: '上架', value: 1 },
                { label: '下架', value: 0 },
                { label: '缺货', value: 2 }
              ]
            },
            {
              prop: 'description',
              label: '商品描述',
              type: 'textarea',
              placeholder: '请输入商品描述',
              rows: 4
            },
            {
              prop: 'tags',
              label: '标签',
              render(h, { value }) {
                return h('el-select', {
                  props: {
                    value: value || [],
                    multiple: true,
                    'allow-create': true,
                    'filterable': true,
                    placeholder: '请选择或输入标签'
                  },
                  style: { width: '100%' },
                  on: {
                    input: (val) => {
                      this.$emit('input', val)
                    }
                  }
                }, [
                  h('el-option', { props: { label: '热销', value: '热销' }}),
                  h('el-option', { props: { label: '新品', value: '新品' }}),
                  h('el-option', { props: { label: '推荐', value: '推荐' }})
                ])
              }
            }
          ]
        }
      }
    }
  },
  methods: {
    // 模拟商品数据获取
    async fetchData(params) {
      console.log('获取商品数据参数:', params)

      return new Promise((resolve) => {
        setTimeout(() => {
          const mockData = {
            data: [
              {
                id: 1,
                name: 'iPhone 14 Pro',
                code: 'IP14P001',
                category: 'electronics',
                price: 7999.00,
                stock: 50,
                status: 1,
                image: 'https://via.placeholder.com/60x60/409EFF/fff?text=Phone',
                createTime: '2023-01-01 10:00:00',
                description: '苹果最新旗舰手机',
                tags: ['热销', '新品']
              },
              {
                id: 2,
                name: '连衣裙',
                code: 'CLO001',
                category: 'clothing',
                price: 299.00,
                stock: 0,
                status: 2,
                image: 'https://via.placeholder.com/60x60/67C23A/fff?text=Dress',
                createTime: '2023-01-02 11:00:00',
                description: '时尚连衣裙',
                tags: ['推荐']
              },
              {
                id: 3,
                name: '有机苹果',
                code: 'FOOD001',
                category: 'food',
                price: 15.80,
                stock: 100,
                status: 1,
                image: 'https://via.placeholder.com/60x60/E6A23C/fff?text=Apple',
                createTime: '2023-01-03 12:00:00',
                description: '新鲜有机苹果',
                tags: ['有机', '新鲜']
              }
            ],
            total: 3
          }
          resolve(mockData)
        }, 1000)
      })
    },

    // 模拟商品数据提交
    async submitData({ mode, data, originalData }) {
      console.log('提交商品数据:', { mode, data, originalData })

      return new Promise((resolve) => {
        setTimeout(() => {
          console.log(`${mode === 'add' ? '新增' : '编辑'}商品成功`)
          resolve(true)
        }, 1000)
      })
    },

    // 处理自定义操作
    handleAction({ action, row, index }) {
      console.log('商品操作:', { action, row, index })

      switch (action) {
        case 'copy':
          this.handleCopy(row)
          break
        case 'toggle':
          this.handleToggleStatus(row, index)
          break
        default:
          this.$message.info(`执行操作: ${action}`)
      }
    },

    // 复制商品
    handleCopy(row) {
      this.$message.success(`复制商品: ${row.name}`)
      // 这里可以实现复制逻辑
    },

    // 切换商品状态
    handleToggleStatus(row, index) {
      const newStatus = row.status === 1 ? 0 : 1
      const statusText = newStatus === 1 ? '上架' : '下架'

      this.$confirm(`确定要${statusText}商品"${row.name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里可以调用API更新状态
        this.$message.success(`${statusText}成功`)
        // 刷新数据
        this.$refs.configurablePage.refresh()
      })
    },

    // 处理删除
    handleDelete({ row, index }) {
      console.log('删除商品:', { row, index })
      this.$message.success(`删除商品: ${row.name}`)
      // 刷新数据
      this.$refs.configurablePage.refresh()
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
      console.log('选中的商品:', selection)
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要删除的商品')
        return
      }

      this.$confirm(`确定要删除选中的 ${this.selectedRows.length} 个商品吗？`, '批量删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里可以调用批量删除API
        this.$message.success(`成功删除 ${this.selectedRows.length} 个商品`)
        this.selectedRows = []
        this.$refs.configurablePage.refresh()
      })
    },

    // 批量导出
    handleBatchExport() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要导出的商品')
        return
      }

      // 这里可以实现导出逻辑
      this.$message.success(`正在导出 ${this.selectedRows.length} 个商品数据...`)
    }
  }
}
</script>

<style lang="scss" scoped>
.advanced-configurable-page-example {
  padding: 20px;

  .batch-actions {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: white;
    padding: 10px 20px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1000;

    .el-button + .el-button {
      margin-left: 10px;
    }
  }
}
</style>
