# ConfigurablePage 配置化页面组件

一个基于JSON配置的Vue页面组件，包含筛选表单、数据表格和修改弹窗表单功能。

## 特性

- 🚀 基于JSON配置，无需编写重复的模板代码
- 📋 支持筛选表单、数据表格、弹窗表单
- 🎨 支持自定义render函数，灵活扩展
- 📱 内置常用组件类型（input、select、date、tag等）
- 🔄 自动处理数据加载、分页、表单提交
- 💡 完整的事件系统，支持自定义操作
- ✨ **增强预设组件** - 类似UI组件库二次封装的完整功能
- 🎯 **完整的参数传递** - 支持任意属性的透传
- 🔄 **动态事件处理** - 支持任意事件的绑定和处理
- 🎨 **插槽支持** - 支持自定义组件内容
- 📦 **预设组件映射** - 内置常用组件的最佳实践配置

## 类型枚举

为了提供更好的类型安全和IDE支持，组件提供了类型枚举常量：

```javascript
import {
  COMPONENT_TYPES,
  BUTTON_TYPES,
  TAG_TYPES
} from '@/components/ConfigurablePage/constants'

// 组件类型
COMPONENT_TYPES.INPUT      // 'input'
COMPONENT_TYPES.SELECT     // 'select'
COMPONENT_TYPES.DATE       // 'date'
COMPONENT_TYPES.TEXTAREA   // 'textarea'
COMPONENT_TYPES.SWITCH     // 'switch'
COMPONENT_TYPES.TAG        // 'tag'
COMPONENT_TYPES.ACTIONS    // 'actions'

// 按钮类型
BUTTON_TYPES.PRIMARY       // 'primary'
BUTTON_TYPES.SUCCESS       // 'success'
BUTTON_TYPES.WARNING       // 'warning'
BUTTON_TYPES.DANGER        // 'danger'
BUTTON_TYPES.INFO          // 'info'

// 标签类型
TAG_TYPES.PRIMARY          // 'primary'
TAG_TYPES.SUCCESS          // 'success'
TAG_TYPES.WARNING          // 'warning'
TAG_TYPES.DANGER           // 'danger'
TAG_TYPES.INFO             // 'info'
```

**推荐使用枚举常量而不是字符串字面量，这样可以：**
- ✅ 避免拼写错误
- ✅ 获得IDE智能提示
- ✅ 便于重构和维护

## 增强预设组件功能 🆕

### 概述

新版本的 ConfigurablePage 支持增强的预设组件功能，提供了类似 UI 组件库二次封装的完整自定义能力。

### 基本用法（向后兼容）

```javascript
// 原有的配置方式仍然有效
{
  prop: 'name',
  label: '姓名',
  type: COMPONENT_TYPES.INPUT,
  placeholder: '请输入姓名'
}
```

### 增强用法

通过 `component` 字段可以获得完整的组件控制能力：

```javascript
{
  prop: 'name',
  label: '姓名',
  type: COMPONENT_TYPES.INPUT,
  component: {
    props: {
      placeholder: '请输入用户姓名',
      clearable: true,
      maxlength: 50,
      'show-word-limit': true
    },
    on: {
      blur: function(event) {
        console.log('输入框失焦', event.target.value)
      },
      clear: function() {
        this.$message.info('已清空姓名')
      }
    },
    slots: {
      prefix: function(h) {
        return h('i', { class: 'el-icon-user' })
      }
    },
    style: {
      width: '300px'
    },
    class: 'custom-input'
  }
}
```

### 支持的配置选项

| 配置项 | 类型 | 说明 |
|--------|------|------|
| props | Object | 组件属性，会合并到组件的 props 中 |
| on | Object | 事件处理函数，支持访问组件实例（符合 Vue 2 渲染函数格式） |
| slots | Object | 插槽内容，支持函数式插槽 |
| style | Object | 组件样式 |
| class | String/Object/Array | 组件类名 |
| attrs | Object | HTML 属性 |

### 事件处理增强

在事件处理函数中，可以访问完整的组件实例（使用 `on` 字段，符合 Vue 2 渲染函数格式）：

```javascript
on: {
  change: function(value) {
    // 访问组件数据
    console.log('筛选表单数据:', this.filterForm)
    console.log('表格数据:', this.tableData)

    // 调用组件方法
    this.loadData()
    this.$message.success('操作成功')

    // 访问其他表单字段
    if (this.filterForm.category) {
      this.loadSubOptions(value)
    }
  }
}
```
- ✅ 运行时类型验证

## 基本用法

```vue
<template>
  <configurable-page
    :config="pageConfig"
    :fetch-data="fetchData"
    :submit-data="submitData"
    @action="handleAction"
    @delete="handleDelete"
  />
</template>

<script>
import ConfigurablePage from '@/components/ConfigurablePage'

export default {
  components: { ConfigurablePage },
  data() {
    return {
      pageConfig: {
        // 配置对象
      }
    }
  },
  methods: {
    async fetchData(params) {
      // 数据获取逻辑
    },
    async submitData({ mode, data, originalData }) {
      // 数据提交逻辑
    }
  }
}
</script>
```

## 配置说明

### 筛选表单配置 (config.filter)

```javascript
filter: {
  title: '筛选条件',           // 表单标题
  labelWidth: '100px',        // 标签宽度
  items: [                    // 表单项配置
    {
      prop: 'name',                    // 字段名
      label: '用户名',                 // 标签
      type: COMPONENT_TYPES.INPUT,     // 组件类型（推荐使用枚举）
      placeholder: '请输入',           // 占位符
      width: '200px',                  // 组件宽度
      defaultValue: '',                // 默认值
      attrs: {}                        // 额外属性
    }
  ]
}
```

### 表格配置 (config.table)

```javascript
table: {
  title: '数据列表',          // 表格标题
  showAdd: true,             // 是否显示新增按钮
  selection: true,           // 是否显示选择列
  pagination: true,          // 是否显示分页（默认true）
  attrs: {                   // 表格属性
    border: true,
    stripe: true
  },
  columns: [                 // 列配置
    {
      prop: 'name',          // 字段名
      label: '姓名',         // 列标题
      width: '120',          // 列宽度
      minWidth: '100',       // 最小宽度
      fixed: 'left',         // 固定列
      sortable: true,        // 是否可排序
      type: 'tag',           // 列类型
      tagMap: [              // 标签映射（type为tag时）
        { value: 1, label: '启用', type: 'success' }
      ],
      actions: [             // 操作按钮（type为actions时）
        { name: 'edit', label: '编辑', type: 'primary' }
      ],
      render: (h, { row, column, index }) => {
        // 自定义渲染函数
        return h('span', row[column.prop])
      }
    }
  ]
}
```

### 弹窗表单配置 (config.dialog)

```javascript
dialog: {
  width: '600px',            // 弹窗宽度
  addTitle: '新增',          // 新增标题
  editTitle: '编辑',         // 编辑标题
  labelWidth: '100px',       // 标签宽度
  attrs: {},                 // 弹窗属性
  rules: {                   // 表单验证规则
    name: [
      { required: true, message: '请输入姓名', trigger: 'blur' }
    ]
  },
  items: [                   // 表单项配置
    {
      prop: 'name',          // 字段名
      label: '姓名',         // 标签
      type: 'input',         // 组件类型
      placeholder: '请输入',  // 占位符
      required: true,        // 是否必填
      defaultValue: '',      // 默认值
      attrs: {},             // 额外属性
      render: (h, { value, item }) => {
        // 自定义渲染函数
      }
    }
  ]
}
```

## 支持的组件类型

### 筛选表单和弹窗表单

- `input` - 输入框
- `textarea` - 文本域
- `select` - 下拉选择
- `date` - 日期选择器
- `switch` - 开关
- `render` - 自定义渲染

### 表格列

- 默认 - 文本显示
- `tag` - 标签显示
- `actions` - 操作按钮
- `render` - 自定义渲染

## 自定义渲染函数

render函数接收参数：
- 筛选表单：`(h, { value, item })`
- 表格列：`(h, { row, column, index })`
- 弹窗表单：`(h, { value, item })`

**⚠️ 重要：render函数必须使用普通函数，不能使用箭头函数**

组件会自动检查render函数类型，如果使用箭头函数会抛出异常：
```
Error: renderFunc must be a regular function, not an arrow function.
Use "render(h, props) {}" instead of "render: (h, props) => {}"
```

在render函数中，`this`指向RenderComponent实例，可以访问：
- `this.$emit(event, value)` - 触发事件
- `this.$props` - 组件props
- `this.$parent` - 父组件实例
- 其他Vue组件实例的属性和方法

```javascript
// ✅ 正确写法 - 使用普通函数
render(h, { value }) {
  // this指向RenderComponent实例
  console.log('组件props:', this.$props)

  return h('el-input', {
    props: {
      value: value,
      placeholder: '自定义输入'
    },
    on: {
      input: (val) => {
        // 使用this.$emit触发事件，会传递给父组件
        this.$emit('input', val)
      }
    }
  })
}

// ❌ 错误写法 - 箭头函数会抛出异常
render: (h, { value }) => {
  // 运行时会抛出异常：renderFunc must be a regular function, not an arrow function
  return h('el-input', { props: { value } })
}
```

### 错误检查机制

组件内置了render函数类型检查：

1. **函数类型检查**：确保renderFunc是一个函数
2. **箭头函数检查**：通过检查`prototype`属性来识别箭头函数
3. **友好的错误提示**：提供明确的错误信息和修复建议

### 复杂render函数示例

```javascript
// 价格范围筛选
{
  prop: 'priceRange',
  label: '价格范围',
  render(h, { value }) {
    return h('div', { style: 'display: flex; align-items: center;' }, [
      h('el-input-number', {
        props: { value: value?.[0], min: 0, placeholder: '最低价' },
        on: {
          input: (val) => {
            const range = value || [null, null]
            range[0] = val
            this.$emit('input', range)
          }
        }
      }),
      h('span', { style: 'margin: 0 10px;' }, '至'),
      h('el-input-number', {
        props: { value: value?.[1], min: 0, placeholder: '最高价' },
        on: {
          input: (val) => {
            const range = value || [null, null]
            range[1] = val
            this.$emit('input', range)
          }
        }
      })
    ])
  }
}
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| config | 页面配置对象 | Object | - |
| fetchData | 数据获取函数 | Function | null |
| submitData | 数据提交函数 | Function | null |

## Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| action | 自定义操作 | { action, row, index } |
| delete | 删除操作 | { row, index } |
| submit | 表单提交 | { mode, data, originalData } |
| selection-change | 选择变化 | selection |

## 方法

| 方法名 | 说明 | 参数 |
|--------|------|------|
| refresh | 刷新数据 | - |
| getSelectedRows | 获取选中行 | - |
| getFilterData | 获取筛选条件 | - |

## 完整示例

参考 `src/views/example/ConfigurablePageExample.vue` 文件查看完整的使用示例。
