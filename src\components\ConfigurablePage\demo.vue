<template>
  <div class="demo-page">
    <h2>ConfigurablePage 增强组件演示</h2>

    <configurable-page
      :config="pageConfig"
      :fetch-data="fetchData"
      :submit-data="submitData"
    />
  </div>
</template>

<script>
import ConfigurablePage from './index.vue'
import { COMPONENT_TYPES } from './constants'

export default {
  name: 'ConfigurablePageDemo',
  components: {
    ConfigurablePage
  },
  data() {
    return {
      pageConfig: {
        filter: {
          title: '用户筛选（增强组件演示）',
          items: [
            // 基础用法
            {
              prop: 'keyword',
              label: '关键词',
              type: COMPONENT_TYPES.INPUT,
              placeholder: '请输入关键词'
            },
            // 增强用法 - 带事件和插槽的输入框
            {
              prop: 'name',
              label: '姓名',
              type: COMPONENT_TYPES.INPUT,
              component: {
                props: {
                  placeholder: '请输入用户姓名',
                  clearable: true,
                  maxlength: 50,
                  'show-word-limit': true
                },
                events: {
                  blur: function(event) {
                    console.log('姓名输入框失焦:', event.target.value)
                    if (event.target.value) {
                      this.$message.info(`输入的姓名: ${event.target.value}`)
                    }
                  },
                  clear: function() {
                    this.$message.success('已清空姓名输入框')
                  }
                },
                slots: {
                  prefix: function(h) {
                    return h('i', { class: 'el-icon-user' })
                  }
                },
                style: {
                  width: '300px'
                }
              }
            },
            // 增强用法 - 带动态交互的选择框
            {
              prop: 'department',
              label: '部门',
              type: COMPONENT_TYPES.SELECT,
              component: {
                options: [
                  { label: '技术部', value: 'tech' },
                  { label: '产品部', value: 'product' },
                  { label: '运营部', value: 'operation' }
                ],
                props: {
                  placeholder: '请选择部门',
                  clearable: true,
                  filterable: true
                },
                events: {
                  change: function(value) {
                    console.log('部门选择变化:', value)
                    this.$message.info(`选择了部门: ${value}`)
                    // 模拟根据部门加载岗位
                    this.loadPositionsByDepartment(value)
                  }
                }
              }
            },
            // 增强用法 - 日期范围选择
            {
              prop: 'dateRange',
              label: '创建时间',
              type: COMPONENT_TYPES.DATE,
              component: {
                props: {
                  type: 'daterange',
                  'range-separator': '至',
                  'start-placeholder': '开始日期',
                  'end-placeholder': '结束日期',
                  format: 'yyyy-MM-dd',
                  'value-format': 'yyyy-MM-dd'
                },
                events: {
                  change: function(value) {
                    console.log('日期范围变化:', value)
                    if (value && value.length === 2) {
                      const [start, end] = value
                      this.$message.info(`选择日期范围: ${start} 至 ${end}`)
                    }
                  }
                }
              }
            }
          ]
        },
        table: {
          title: '用户列表',
          showAdd: true,
          columns: [
            { prop: 'id', label: 'ID', width: '80' },
            { prop: 'name', label: '姓名', width: '120' },
            { prop: 'department', label: '部门', width: '120' },
            { prop: 'email', label: '邮箱', minWidth: '200' },
            {
              prop: 'status',
              label: '状态',
              width: '100',
              type: 'tag',
              tagMap: [
                { value: 1, label: '启用', type: 'success' },
                { value: 0, label: '禁用', type: 'danger' }
              ]
            },
            {
              prop: 'actions',
              label: '操作',
              type: 'actions',
              width: '150',
              actions: [
                { name: 'edit', label: '编辑', type: 'text' },
                { name: 'delete', label: '删除', type: 'text' }
              ]
            }
          ]
        },
        dialog: {
          title: '用户信息',
          items: [
            {
              prop: 'name',
              label: '姓名',
              type: COMPONENT_TYPES.INPUT,
              required: true,
              component: {
                props: {
                  placeholder: '请输入用户姓名',
                  clearable: true,
                  maxlength: 50,
                  'show-word-limit': true
                }
              }
            },
            {
              prop: 'email',
              label: '邮箱',
              type: COMPONENT_TYPES.INPUT,
              component: {
                props: {
                  placeholder: '请输入邮箱地址',
                  clearable: true,
                  type: 'email'
                },
                events: {
                  blur: function(event) {
                    const email = event.target.value
                    if (email && !this.validateEmail(email)) {
                      this.$message.warning('请输入正确的邮箱格式')
                    }
                  }
                }
              }
            },
            {
              prop: 'department',
              label: '部门',
              type: COMPONENT_TYPES.SELECT,
              component: {
                options: [
                  { label: '技术部', value: 'tech' },
                  { label: '产品部', value: 'product' },
                  { label: '运营部', value: 'operation' }
                ],
                props: {
                  placeholder: '请选择部门'
                }
              }
            },
            {
              prop: 'isActive',
              label: '是否启用',
              type: COMPONENT_TYPES.SWITCH,
              component: {
                props: {
                  'active-text': '启用',
                  'inactive-text': '禁用'
                },
                events: {
                  change: function(value) {
                    this.$message.info(`用户状态已${value ? '启用' : '禁用'}`)
                  }
                }
              }
            }
          ]
        }
      }
    }
  },
  methods: {
    // 模拟数据获取
    async fetchData(params) {
      console.log('获取数据参数:', params)

      // 模拟API调用
      return new Promise(resolve => {
        setTimeout(() => {
          resolve({
            data: [
              { id: 1, name: '张三', department: '技术部', email: '<EMAIL>', status: 1 },
              { id: 2, name: '李四', department: '产品部', email: '<EMAIL>', status: 1 },
              { id: 3, name: '王五', department: '运营部', email: '<EMAIL>', status: 0 }
            ],
            total: 3
          })
        }, 1000)
      })
    },

    // 模拟数据提交
    async submitData(data) {
      console.log('提交数据:', data)

      // 模拟API调用
      return new Promise(resolve => {
        setTimeout(() => {
          resolve(true)
        }, 1000)
      })
    },

    // 根据部门加载岗位（演示方法）
    loadPositionsByDepartment(department) {
      console.log('加载部门岗位:', department)
      // 这里可以实现具体的岗位加载逻辑
    },

    // 邮箱验证
    validateEmail(email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return emailRegex.test(email)
    }
  }
}
</script>

<style scoped>
.demo-page {
  padding: 20px;
}

h2 {
  margin-bottom: 20px;
  color: #303133;
}
</style>
