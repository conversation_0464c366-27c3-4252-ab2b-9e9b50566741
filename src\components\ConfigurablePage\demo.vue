<template>
  <div class="demo-container">
    <h2>ConfigurablePage 新配置格式演示</h2>
    <p>使用 options 字段配置组件（对应 Vue 2 h 函数第二个参数格式）</p>

    <configurable-page
      :config="demoConfig"
      @search="handleSearch"
      @add="handleAdd"
      @edit="handleEdit"
      @delete="handleDelete"
    />
  </div>
</template>

<script>
import ConfigurablePage from './index.vue'
import { COMPONENT_TYPES } from './constants'

export default {
  name: 'ConfigurablePageDemo',
  components: {
    ConfigurablePage
  },
  data() {
    return {
      demoConfig: {
        filter: {
          title: '用户筛选',
          items: [
            {
              prop: 'name',
              label: '姓名',
              type: COMPONENT_TYPES.INPUT,
              config: {
                props: {
                  placeholder: '请输入姓名',
                  clearable: true,
                  maxlength: 50
                },
                on: {
                  change: (value) => {
                    console.log('姓名变化:', value)
                  }
                }
              }
            },
            {
              prop: 'status',
              label: '状态',
              type: COMPONENT_TYPES.SELECT,
              options: [
                { label: '启用', value: 1 },
                { label: '禁用', value: 0 }
              ]
            },
            {
              prop: 'age',
              label: '年龄',
              type: COMPONENT_TYPES.INPUT_NUMBER,
              config: {
                props: {
                  min: 1,
                  max: 120,
                  step: 1
                }
              }
            },
            {
              prop: 'rating',
              label: '评分',
              type: COMPONENT_TYPES.RATE,
              config: {
                props: {
                  max: 5,
                  'allow-half': true
                }
              }
            }
          ]
        },
        table: {
          title: '用户列表',
          columns: [
            { prop: 'name', label: '姓名', width: 120 },
            { prop: 'age', label: '年龄', width: 80 },
            { prop: 'status', label: '状态', width: 100, type: 'tag' },
            { prop: 'rating', label: '评分', width: 120 },
            { prop: 'createTime', label: '创建时间', width: 180 },
            { prop: 'actions', label: '操作', width: 200, type: 'actions' }
          ],
          data: [
            {
              id: 1,
              name: '张三',
              age: 25,
              status: 1,
              rating: 4.5,
              createTime: '2023-01-01'
            },
            {
              id: 2,
              name: '李四',
              age: 30,
              status: 0,
              rating: 3.8,
              createTime: '2023-01-02'
            }
          ]
        },
        dialog: {
          title: '用户信息',
          items: [
            {
              prop: 'name',
              label: '姓名',
              type: COMPONENT_TYPES.INPUT,
              required: true,
              config: {
                props: {
                  placeholder: '请输入姓名',
                  clearable: true
                }
              }
            },
            {
              prop: 'age',
              label: '年龄',
              type: COMPONENT_TYPES.INPUT_NUMBER,
              config: {
                props: {
                  min: 1,
                  max: 120
                }
              }
            },
            {
              prop: 'status',
              label: '状态',
              type: COMPONENT_TYPES.SELECT,
              options: [
                { label: '启用', value: 1 },
                { label: '禁用', value: 0 }
              ]
            },
            {
              prop: 'description',
              label: '描述',
              type: COMPONENT_TYPES.TEXTAREA,
              config: {
                props: {
                  rows: 4,
                  placeholder: '请输入描述信息'
                }
              }
            }
          ]
        }
      }
    }
  },
  methods: {
    handleSearch(params) {
      console.log('搜索参数:', params)
    },
    handleAdd() {
      console.log('添加用户')
    },
    handleEdit(row) {
      console.log('编辑用户:', row)
    },
    handleDelete(row) {
      console.log('删除用户:', row)
    }
  }
}
</script>

<style scoped>
.demo-container {
  padding: 20px;
}

.demo-container h2 {
  color: #409EFF;
  margin-bottom: 10px;
}

.demo-container p {
  color: #666;
  margin-bottom: 20px;
}
</style>
