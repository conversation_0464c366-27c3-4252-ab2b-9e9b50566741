import { shallowMount, createLocalVue } from '@vue/test-utils'
import ElementUI from 'element-ui'
import ConfigurablePage from '@/components/ConfigurablePage/index.vue'

const localVue = createLocalVue()
localVue.use(ElementUI)

describe('ConfigurablePage.vue', () => {
  let wrapper

  const mockConfig = {
    filter: {
      title: '测试筛选',
      items: [
        {
          prop: 'name',
          label: '姓名',
          type: 'input',
          placeholder: '请输入姓名'
        }
      ]
    },
    table: {
      title: '测试表格',
      showAdd: true,
      columns: [
        {
          prop: 'id',
          label: 'ID',
          width: '80'
        },
        {
          prop: 'name',
          label: '姓名',
          minWidth: '120'
        }
      ]
    },
    dialog: {
      addTitle: '新增',
      editTitle: '编辑',
      items: [
        {
          prop: 'name',
          label: '姓名',
          type: 'input',
          required: true
        }
      ]
    }
  }

  const mockFetchData = jest.fn().mockResolvedValue({
    data: [
      { id: 1, name: '张三' },
      { id: 2, name: '李四' }
    ],
    total: 2
  })

  const mockSubmitData = jest.fn().mockResolvedValue(true)

  beforeEach(() => {
    wrapper = shallowMount(ConfigurablePage, {
      localVue,
      propsData: {
        config: mockConfig,
        fetchData: mockFetchData,
        submitData: mockSubmitData
      }
    })
  })

  afterEach(() => {
    wrapper.destroy()
  })

  it('renders correctly', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.configurable-page').exists()).toBe(true)
  })

  it('initializes filter form correctly', () => {
    expect(wrapper.vm.filterForm).toEqual({
      name: ''
    })
  })

  it('initializes dialog form correctly', () => {
    expect(wrapper.vm.dialogForm).toEqual({
      name: ''
    })
  })

  it('calls fetchData on mount', () => {
    expect(mockFetchData).toHaveBeenCalled()
  })

  it('handles search correctly', async () => {
    wrapper.vm.filterForm.name = '张三'
    await wrapper.vm.handleSearch()

    expect(wrapper.vm.pagination.page).toBe(1)
    expect(mockFetchData).toHaveBeenCalledWith({
      name: '张三',
      page: 1,
      size: 10
    })
  })

  it('handles reset correctly', async () => {
    wrapper.vm.filterForm.name = '张三'
    await wrapper.vm.handleReset()

    expect(wrapper.vm.filterForm.name).toBe('')
    expect(wrapper.vm.pagination.page).toBe(1)
  })

  it('handles add correctly', () => {
    wrapper.vm.handleAdd()

    expect(wrapper.vm.dialogMode).toBe('add')
    expect(wrapper.vm.dialogVisible).toBe(true)
    expect(wrapper.vm.currentEditRow).toBe(null)
  })

  it('handles edit correctly', () => {
    const row = { id: 1, name: '张三' }
    wrapper.vm.handleEdit(row, 0)

    expect(wrapper.vm.dialogMode).toBe('edit')
    expect(wrapper.vm.dialogVisible).toBe(true)
    expect(wrapper.vm.currentEditRow).toEqual({ ...row, _index: 0 })
    expect(wrapper.vm.dialogForm.name).toBe('张三')
  })

  it('handles pagination correctly', async () => {
    await wrapper.vm.handleCurrentChange(2)
    expect(wrapper.vm.pagination.page).toBe(2)

    await wrapper.vm.handleSizeChange(20)
    expect(wrapper.vm.pagination.size).toBe(20)
    expect(wrapper.vm.pagination.page).toBe(1)
  })

  it('gets tag type and text correctly', () => {
    const tagMap = [
      { value: 1, label: '启用', type: 'success' },
      { value: 0, label: '禁用', type: 'danger' }
    ]

    expect(wrapper.vm.getTagType(1, tagMap)).toBe('success')
    expect(wrapper.vm.getTagText(1, tagMap)).toBe('启用')
    expect(wrapper.vm.getTagType(2, tagMap)).toBe('')
    expect(wrapper.vm.getTagText(2, tagMap)).toBe(2)
  })

  it('provides public methods', () => {
    expect(typeof wrapper.vm.refresh).toBe('function')
    expect(typeof wrapper.vm.getSelectedRows).toBe('function')
    expect(typeof wrapper.vm.getFilterData).toBe('function')
  })

  it('emits events correctly', () => {
    const selection = [{ id: 1, name: '张三' }]
    wrapper.vm.handleSelectionChange(selection)

    expect(wrapper.emitted('selection-change')).toBeTruthy()
    expect(wrapper.emitted('selection-change')[0]).toEqual([selection])
  })

  it('handles custom render functions with regular function', async () => {
    const config = {
      filter: {
        items: [
          {
            prop: 'customField',
            label: '自定义字段',
            render(h, { value }) {
              return h('div', { class: 'custom-render' }, value)
            }
          }
        ]
      },
      table: { columns: [] },
      dialog: { items: [] }
    }

    const wrapper = mount(ConfigurablePage, {
      propsData: { config }
    })

    // 应该能正常渲染
    expect(wrapper.find('.custom-render').exists()).toBe(true)
  })

  it('throws error for arrow function render', () => {
    const config = {
      filter: {
        items: [
          {
            prop: 'arrowField',
            label: '箭头函数字段',
            render: (h, { value }) => {
              return h('div', value)
            }
          }
        ]
      },
      table: { columns: [] },
      dialog: { items: [] }
    }

    // 应该抛出异常
    expect(() => {
      mount(ConfigurablePage, {
        propsData: { config }
      })
    }).toThrow('renderFunc must be a regular function, not an arrow function')
  })
})
