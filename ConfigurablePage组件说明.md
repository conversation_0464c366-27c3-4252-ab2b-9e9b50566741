# ConfigurablePage 配置化页面组件

## 概述

我已经为您创建了一个功能完整的配置化Page组件，该组件基于JSON配置的方式开发，包含筛选表单、数据表格和修改弹窗表单三个主要功能模块。

## 文件结构

```
src/
├── components/
│   └── ConfigurablePage/
│       ├── index.vue          # 主组件文件
│       └── README.md          # 详细使用文档
├── views/
│   └── example/
│       ├── ConfigurablePageExample.vue         # 基础使用示例
│       └── AdvancedConfigurablePageExample.vue # 高级功能示例
├── router/
│   └── modules/
│       └── configurable-page.js # 路由配置
└── tests/
    └── unit/
        └── ConfigurablePage.spec.js # 单元测试
```

## 核心特性

### 1. 筛选表单 (Filter Form)
- ✅ 支持多种表单组件：input、select、date、textarea、switch
- ✅ 支持自定义render函数
- ✅ 自动处理表单重置和搜索
- ✅ 灵活的布局配置

### 2. 数据表格 (Data Table)
- ✅ 支持分页、排序、选择
- ✅ 内置操作列（编辑、删除等）
- ✅ 支持标签显示、图片预览等特殊列类型
- ✅ 自定义render函数支持
- ✅ 响应式设计

### 3. 弹窗表单 (Dialog Form)
- ✅ 新增/编辑模式自动切换
- ✅ 表单验证支持
- ✅ 多种表单组件支持
- ✅ 自定义render函数
- ✅ 自动数据填充和重置

### 4. 高级功能
- ✅ 事件系统完整（action、delete、submit等）
- ✅ 批量操作支持
- ✅ 数据加载状态管理
- ✅ 错误处理机制
- ✅ 公共方法暴露（refresh、getSelectedRows等）

## 配置示例

### 基础配置
```javascript
const pageConfig = {
  filter: {
    title: '筛选条件',
    items: [
      {
        prop: 'name',
        label: '姓名',
        type: 'input',
        placeholder: '请输入姓名'
      }
    ]
  },
  table: {
    title: '数据列表',
    showAdd: true,
    columns: [
      { prop: 'id', label: 'ID', width: '80' },
      { prop: 'name', label: '姓名', minWidth: '120' }
    ]
  },
  dialog: {
    addTitle: '新增',
    editTitle: '编辑',
    items: [
      {
        prop: 'name',
        label: '姓名',
        type: 'input',
        required: true
      }
    ]
  }
}
```

### 自定义Render函数示例
```javascript
// 价格范围筛选
{
  prop: 'priceRange',
  label: '价格范围',
  render: (h, { value, item }) => {
    return h('div', { style: 'display: flex;' }, [
      h('el-input-number', {
        props: { value: value?.[0], min: 0 },
        on: { input: (val) => this.$emit('input', [val, value?.[1]]) }
      }),
      h('span', { style: 'margin: 0 10px;' }, '至'),
      h('el-input-number', {
        props: { value: value?.[1], min: 0 },
        on: { input: (val) => this.$emit('input', [value?.[0], val]) }
      })
    ])
  }
}

// 图片预览列
{
  prop: 'image',
  label: '图片',
  render: (h, { row }) => {
    return h('el-image', {
      props: { src: row.image, fit: 'cover' },
      style: { width: '60px', height: '60px' }
    })
  }
}
```

## 使用方法

### 1. 基础使用
```vue
<template>
  <configurable-page
    :config="pageConfig"
    :fetch-data="fetchData"
    :submit-data="submitData"
    @action="handleAction"
    @delete="handleDelete"
  />
</template>

<script>
import ConfigurablePage from '@/components/ConfigurablePage'

export default {
  components: { ConfigurablePage },
  data() {
    return { pageConfig: { /* 配置对象 */ } }
  },
  methods: {
    async fetchData(params) {
      // 数据获取逻辑
      return { data: [], total: 0 }
    },
    async submitData({ mode, data, originalData }) {
      // 数据提交逻辑
      return true
    }
  }
}
</script>
```

### 2. 访问示例页面
启动项目后，可以通过以下路径访问示例：
- `/configurable-page/basic` - 基础功能示例
- `/configurable-page/advanced` - 高级功能示例

## 测试

运行单元测试：
```bash
npm run test:unit -- ConfigurablePage.spec.js
```

## 扩展性

### 1. 添加新的组件类型
在组件的template中添加新的条件分支：
```vue
<el-custom-component
  v-else-if="item.type === 'custom'"
  v-model="formData[item.prop]"
  v-bind="item.attrs"
/>
```

### 2. 添加新的列类型
在表格列的template中添加新的条件分支：
```vue
<custom-column-component
  v-else-if="column.type === 'custom'"
  :row="scope.row"
  :column="column"
/>
```

### 3. 自定义事件
组件支持完整的事件系统，可以监听各种操作：
```vue
<configurable-page
  @action="handleCustomAction"
  @selection-change="handleSelectionChange"
  @submit="handleFormSubmit"
/>
```

## 优势

1. **开发效率高**：通过JSON配置即可快速构建复杂的管理页面
2. **可维护性强**：配置与逻辑分离，易于维护和修改
3. **扩展性好**：支持自定义render函数，可以实现任意复杂的UI
4. **复用性强**：一个组件可以适配多种业务场景
5. **类型安全**：完整的props和events定义
6. **测试覆盖**：提供完整的单元测试

这个配置化Page组件可以大大提高您的开发效率，减少重复代码，同时保持足够的灵活性来应对各种复杂的业务需求。
