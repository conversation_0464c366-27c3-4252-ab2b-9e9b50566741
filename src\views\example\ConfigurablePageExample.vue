<template>
  <div class="configurable-page-example">
    <configurable-page
      :config="pageConfig"
      :fetch-data="fetchData"
      :submit-data="submitData"
      @action="handleAction"
      @delete="handleDelete"
      @selection-change="handleSelectionChange"
    />
  </div>
</template>

<script>
import ConfigurablePage from '@/components/ConfigurablePage'
import { COMPONENT_TYPES, BUTTON_TYPES, TAG_TYPES } from '@/components/ConfigurablePage/constants'

export default {
  name: 'ConfigurablePageExample',
  components: {
    ConfigurablePage
  },
  data() {
    return {
      statusOptions: [
        { label: '全部', value: '' },
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ],
      pageConfig: {
        // 筛选表单配置
        filter: {
          title: '用户筛选',
          form: {
            attrs: {
              labelWidth: ''
            },
            items: [
              {
                prop: 'name',
                label: '用户名',
                type: COMPONENT_TYPES.INPUT,
                config: {
                  props: {
                    placeholder: '请输入用户名',
                    clearable: true
                  }
                }
              },
              {
                prop: 'status',
                label: '状态',
                type: COMPONENT_TYPES.SELECT,
                options: this.statusOptions
              },
              {
                prop: 'createTime',
                label: '创建时间',
                type: COMPONENT_TYPES.DATE_RANGE
              },
              {
                prop: 'customField',
                label: '自定义字段',
                render(h, { value }) {
                  return (
                    <el-input
                      value={value}
                      placeholder='自定义输入框'
                      onInput={(val) => {
                        this.$emit('input', val)
                      }}
                    />
                  )
                }
              }
            ]
          }
        },

        // 表格配置
        table: {
          title: '用户列表',
          showAdd: true,
          selection: true,
          attrs: {
            border: true,
            stripe: true
          },
          columns: [
            {
              prop: 'id',
              label: 'ID',
              width: '80'
            },
            {
              prop: 'name',
              label: '用户名',
              minWidth: '120'
            },
            {
              prop: 'email',
              label: '邮箱',
              minWidth: '180'
            },
            {
              prop: 'status',
              label: '状态',
              width: '100',
              type: COMPONENT_TYPES.TAG,
              tagMap: [
                { value: 1, label: '启用', type: TAG_TYPES.SUCCESS },
                { value: 0, label: '禁用', type: TAG_TYPES.DANGER }
              ]
            },
            {
              prop: 'createTime',
              label: '创建时间',
              width: '180'
            },
            {
              prop: 'avatar',
              label: '头像',
              width: '100',
              render: (h, { row }) => {
                return h('el-avatar', {
                  props: {
                    size: 40,
                    src: row.avatar
                  }
                })
              }
            },
            {
              prop: 'actions',
              label: '操作',
              width: '200',
              fixed: 'right',
              type: COMPONENT_TYPES.ACTIONS,
              actions: [
                { name: 'edit', label: '编辑', type: BUTTON_TYPES.PRIMARY },
                { name: 'delete', label: '删除', type: BUTTON_TYPES.DANGER },
                { name: 'detail', label: '详情', type: BUTTON_TYPES.INFO }
              ]
            }
          ]
        },

        // 弹窗表单配置
        drawer: {
          addTitle: '新增用户',
          editTitle: '编辑用户',
          attrs: {
            // width: '800px'
          },
          form: {
            attrs: {
              labelPosition: 'right',
              labelWidth: '80px'
            },
            rules: {
              name: [
                { required: true, message: '请输入用户名', trigger: 'blur' }
              ],
              email: [
                { required: true, message: '请输入邮箱', trigger: 'blur' },
                { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
              ]
            },
            items: [
              {
                prop: 'name',
                label: '用户名',
                type: COMPONENT_TYPES.INPUT,
                required: true
              },
              {
                prop: 'email',
                label: '邮箱',
                type: COMPONENT_TYPES.INPUT,
                required: true
              },
              {
                prop: 'phone',
                label: '手机号',
                type: COMPONENT_TYPES.INPUT
              },
              {
                prop: 'status',
                label: '状态',
                type: COMPONENT_TYPES.SELECT,
                defaultValue: 1,
                options: [
                  { label: '启用', value: 1 },
                  { label: '禁用', value: 0 }
                ]
              },
              {
                prop: 'description',
                label: '描述',
                type: COMPONENT_TYPES.TEXTAREA,
                rows: 3
              },
              {
                prop: 'tags',
                label: '标签',
                render(h, { value }) {
                  return h('el-select', {
                    props: {
                      value: value,
                      multiple: true,
                      placeholder: '请选择标签'
                    },
                    on: {
                      input: (val) => {
                        this.$emit('input', val)
                      }
                    }
                  }, [
                    h('el-option', { props: { label: '前端', value: 'frontend' }}),
                    h('el-option', { props: { label: '后端', value: 'backend' }}),
                    h('el-option', { props: { label: '全栈', value: 'fullstack' }})
                  ])
                }
              }
            ]
          }
        }
      }
    }
  },
  created() {
    setTimeout(() => {
      this.statusOptions.push({ label: '待审核', value: 2 })
    }, 2000)
  },
  methods: {
    // 模拟数据获取
    async fetchData(params) {
      console.log('获取数据参数:', params)

      // 模拟API调用
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockData = {
            data: [
              {
                id: 1,
                name: '张三',
                email: '<EMAIL>',
                phone: '13800138001',
                status: 1,
                createTime: '2023-01-01 10:00:00',
                avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
                description: '前端开发工程师',
                tags: ['frontend']
              },
              {
                id: 2,
                name: '李四',
                email: '<EMAIL>',
                phone: '13800138002',
                status: 0,
                createTime: '2023-01-02 11:00:00',
                avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
                description: '后端开发工程师',
                tags: ['backend']
              }
            ],
            total: 2
          }
          resolve(mockData)
        }, 1000)
      })
    },

    // 模拟数据提交
    async submitData({ mode, data, originalData }) {
      console.log('提交数据:', { mode, data, originalData })

      // 模拟API调用
      return new Promise((resolve) => {
        setTimeout(() => {
          console.log(`${mode === 'add' ? '新增' : '编辑'}成功`)
          resolve(true)
        }, 1000)
      })
    },

    // 处理自定义操作
    handleAction({ action, row, index }) {
      console.log('自定义操作:', { action, row, index })

      if (action === 'detail') {
        this.$message.info(`查看用户详情: ${row.name}`)
      }
    },

    // 处理删除
    handleDelete({ row, index }) {
      console.log('删除用户:', { row, index })
      this.$message.success(`删除用户: ${row.name}`)
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      console.log('选中的行:', selection)
    }
  }
}
</script>

<style lang="scss" scoped>
.configurable-page-example {
  padding: 20px;
}
</style>
