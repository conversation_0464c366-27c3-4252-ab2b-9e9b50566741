/**
 * ConfigurablePage 增强组件使用示例
 * 展示如何使用新的增强预设组件功能
 */

import { COMPONENT_TYPES } from './constants'
import { createPresetComponentConfig, createSimplePresetConfig } from './PresetComponent'

/**
 * 基础使用示例
 */
export const basicExample = {
  filter: {
    title: '用户筛选',
    items: [
      {
        prop: 'name',
        label: '姓名',
        type: COMPONENT_TYPES.INPUT,
        placeholder: '请输入姓名'
      },
      {
        prop: 'status',
        label: '状态',
        type: COMPONENT_TYPES.SELECT,
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 }
        ]
      }
    ]
  }
}

/**
 * 增强功能示例 - 使用 component 字段
 */
export const enhancedExample = {
  filter: {
    title: '高级用户筛选',
    items: [
      {
        prop: 'name',
        label: '姓名',
        type: COMPONENT_TYPES.INPUT,
        component: {
          props: {
            placeholder: '请输入用户姓名',
            clearable: true,
            maxlength: 50,
            'show-word-limit': true
          },
          on: {
            blur: function(event) {
              console.log('姓名输入框失焦', event.target.value)
            },
            clear: function() {
              console.log('清空姓名输入框')
              // 可以访问组件实例的方法
              this.$message.info('已清空姓名')
            }
          },
          style: {
            width: '300px'
          },
          class: 'enhanced-input'
        }
      },
      {
        prop: 'email',
        label: '邮箱',
        type: COMPONENT_TYPES.INPUT,
        component: {
          props: {
            placeholder: '请输入邮箱地址',
            clearable: true,
            type: 'email'
          },
          slots: {
            prefix: function(h) {
              return h('i', { class: 'el-icon-message' })
            }
          },
          on: {
            change: function(value) {
              // 简单的邮箱格式验证
              const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
              if (value && !emailRegex.test(value)) {
                this.$message.warning('请输入正确的邮箱格式')
              }
            }
          }
        }
      },
      {
        prop: 'department',
        label: '部门',
        type: COMPONENT_TYPES.SELECT,
        component: {
          options: [
            { label: '技术部', value: 'tech' },
            { label: '产品部', value: 'product' },
            { label: '运营部', value: 'operation' },
            { label: '人事部', value: 'hr' }
          ],
          props: {
            placeholder: '请选择部门',
            clearable: true,
            filterable: true,
            multiple: false
          },
          on: {
            change: function(value) {
              console.log('部门选择变化', value)
              // 根据部门选择加载对应的岗位数据
              this.loadPositionsByDepartment(value)
            },
            'visible-change': function(visible) {
              if (visible) {
                console.log('部门下拉框展开')
              }
            }
          }
        }
      },
      {
        prop: 'dateRange',
        label: '创建时间',
        type: COMPONENT_TYPES.DATE,
        component: {
          props: {
            type: 'daterange',
            'range-separator': '至',
            'start-placeholder': '开始日期',
            'end-placeholder': '结束日期',
            format: 'yyyy-MM-dd',
            'value-format': 'yyyy-MM-dd'
          },
          on: {
            change: function(value) {
              console.log('日期范围变化', value)
              if (value && value.length === 2) {
                const [startDate, endDate] = value
                const daysDiff = (new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24)
                if (daysDiff > 365) {
                  this.$message.warning('日期范围不能超过一年')
                  // 可以重置日期
                  this.filterForm.dateRange = null
                }
              }
            }
          }
        }
      }
    ]
  },
  dialog: {
    title: '用户信息',
    items: [
      {
        prop: 'name',
        label: '姓名',
        type: COMPONENT_TYPES.INPUT,
        required: true,
        component: {
          props: {
            placeholder: '请输入用户姓名',
            clearable: true,
            maxlength: 50,
            'show-word-limit': true
          }
        }
      },
      {
        prop: 'bio',
        label: '个人简介',
        type: COMPONENT_TYPES.TEXTAREA,
        component: {
          props: {
            placeholder: '请输入个人简介',
            rows: 4,
            maxlength: 500,
            'show-word-limit': true,
            autosize: { minRows: 3, maxRows: 8 }
          }
        }
      },
      {
        prop: 'isActive',
        label: '是否启用',
        type: COMPONENT_TYPES.SWITCH,
        component: {
          props: {
            'active-text': '启用',
            'inactive-text': '禁用',
            'active-color': '#13ce66',
            'inactive-color': '#ff4949'
          },
          on: {
            change: function(value) {
              console.log('开关状态变化', value)
              this.$message.info(`用户状态已${value ? '启用' : '禁用'}`)
            }
          }
        }
      }
    ]
  }
}

/**
 * 使用工具函数创建配置的示例
 */
export const utilityExample = {
  filter: {
    title: '使用工具函数的示例',
    items: [
      // 使用 createSimplePresetConfig 创建简单配置
      createSimplePresetConfig(COMPONENT_TYPES.INPUT, {
        placeholder: '请输入关键词',
        clearable: true
      }, {
        change: function(value) {
          console.log('关键词变化', value)
        }
      }),

      // 使用 createPresetComponentConfig 创建完整配置
      createPresetComponentConfig(COMPONENT_TYPES.SELECT, {
        options: [
          { label: '全部', value: '' },
          { label: '已发布', value: 'published' },
          { label: '草稿', value: 'draft' }
        ],
        props: {
          placeholder: '请选择状态'
        },
        on: {
          change: function(value) {
            this.loadData()
          }
        }
      })
    ]
  }
}

/**
 * 复杂交互示例
 */
export const complexInteractionExample = {
  filter: {
    title: '复杂交互示例',
    items: [
      {
        prop: 'category',
        label: '分类',
        type: COMPONENT_TYPES.SELECT,
        component: {
          options: [], // 动态加载
          props: {
            placeholder: '请选择分类',
            clearable: true,
            loading: false
          },
          on: {
            'visible-change': function(visible) {
              if (visible && this.categoryOptions.length === 0) {
                this.loadCategoryOptions()
              }
            },
            change: function(value) {
              // 分类变化时，重置子分类
              this.filterForm.subCategory = ''
              this.loadSubCategoryOptions(value)
            }
          }
        }
      },
      {
        prop: 'subCategory',
        label: '子分类',
        type: COMPONENT_TYPES.SELECT,
        component: {
          options: [], // 根据分类动态加载
          props: {
            placeholder: '请先选择分类',
            clearable: true,
            disabled: false // 根据分类选择状态动态控制
          }
        }
      }
    ]
  }
}
