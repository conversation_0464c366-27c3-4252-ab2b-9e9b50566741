/**
 * ConfigurablePage 新配置格式示例
 * 使用 options 字段来配置组件（对应 Vue 2 h 函数第二个参数）
 */

import { COMPONENT_TYPES } from './constants'

/**
 * 基础使用示例 - 新的 options 格式
 */
export const basicExample = {
  filter: {
    title: '用户筛选',
    items: [
      {
        prop: 'name',
        label: '姓名',
        type: COMPONENT_TYPES.INPUT,
        config: {
          props: {
            placeholder: '请输入姓名',
            clearable: true,
            maxlength: 50
          },
          on: {
            change: function(value) {
              console.log('姓名变化:', value)
            }
          }
        }
      },
      {
        prop: 'status',
        label: '状态',
        type: COMPONENT_TYPES.SELECT,
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 }
        ]
      },
      {
        prop: 'age',
        label: '年龄',
        type: COMPONENT_TYPES.INPUT_NUMBER,
        config: {
          props: {
            min: 1,
            max: 120,
            step: 1
          }
        }
      },
      {
        prop: 'createTime',
        label: '创建时间',
        type: COMPONENT_TYPES.DATE_PICKER,
        config: {
          props: {
            type: 'date',
            format: 'yyyy-MM-dd',
            'value-format': 'yyyy-MM-dd'
          }
        }
      }
    ]
  },
  dialog: {
    title: '用户信息',
    items: [
      {
        prop: 'name',
        label: '姓名',
        type: COMPONENT_TYPES.INPUT,
        required: true,
        config: {
          props: {
            placeholder: '请输入姓名',
            clearable: true
          }
        }
      },
      {
        prop: 'description',
        label: '描述',
        type: COMPONENT_TYPES.TEXTAREA,
        config: {
          props: {
            rows: 4,
            placeholder: '请输入描述信息'
          }
        }
      }
    ]
  }
}

/**
 * 高级功能示例 - 展示各种组件类型
 */
export const advancedExample = {
  filter: {
    title: '高级筛选',
    items: [
      {
        prop: 'radio',
        label: '单选',
        type: COMPONENT_TYPES.RADIO,
        options: [
          { label: '选项1', value: 1 },
          { label: '选项2', value: 2 },
          { label: '选项3', value: 3 }
        ]
      },
      {
        prop: 'cascader',
        label: '级联选择',
        type: COMPONENT_TYPES.CASCADER,
        config: {
          props: {
            options: [
              {
                value: 'beijing',
                label: '北京',
                children: [
                  { value: 'chaoyang', label: '朝阳区' },
                  { value: 'haidian', label: '海淀区' }
                ]
              },
              {
                value: 'shanghai',
                label: '上海',
                children: [
                  { value: 'huangpu', label: '黄浦区' },
                  { value: 'pudong', label: '浦东新区' }
                ]
              }
            ],
            'show-all-levels': false
          }
        }
      },
      {
        prop: 'slider',
        label: '滑块',
        type: COMPONENT_TYPES.SLIDER,
        config: {
          props: {
            min: 0,
            max: 100,
            step: 10
          },
          on: {
            change: function(value) {
              console.log('滑块值变化:', value)
            }
          }
        }
      },
      {
        prop: 'rate',
        label: '评分',
        type: COMPONENT_TYPES.RATE,
        config: {
          props: {
            max: 5,
            'allow-half': true,
            'show-text': true
          }
        }
      }
    ]
  }
}

/**
 * 事件处理示例
 */
export const eventExample = {
  dialog: {
    title: '事件处理示例',
    items: [
      {
        prop: 'input',
        label: '输入框',
        type: COMPONENT_TYPES.INPUT,
        config: {
          props: {
            placeholder: '请输入内容'
          },
          on: {
            focus: function() {
              console.log('输入框获得焦点')
            },
            blur: function() {
              console.log('输入框失去焦点')
            },
            change: function(value) {
              console.log('输入框值变化:', value)
            }
          }
        }
      },
      {
        prop: 'select',
        label: '下拉选择',
        type: COMPONENT_TYPES.SELECT,
        options: [
          { label: '选项1', value: 1 },
          { label: '选项2', value: 2 }
        ]
      }
    ]
  }
}
